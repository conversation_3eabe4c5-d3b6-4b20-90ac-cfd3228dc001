# Guia do Mock de Rastreamento de Localização

## Como Funcionar

O sistema de mock simula pessoas caminhando de forma realística e respeita a variável de ambiente `NEXT_PUBLIC_USE_MOCK_DATA` para alternar entre dados mock e API real.

## Configuração

### Para usar dados mock (recomendado para testes):
```env
NEXT_PUBLIC_USE_MOCK_DATA=true
```

### Para usar API real:
```env
NEXT_PUBLIC_USE_MOCK_DATA=false
```

## Comportamentos Simulados

### Tipos de Movimento

1. **`walking`** (Caminhada Normal)
   - Velocidade: 1.4 m/s (normal)
   - Pausas ocasionais de 2-7 segundos
   - Movimento consistente em direção a alvos aleatórios

2. **`erratic`** (Movimento Errático)
   - Velocidade variável: 2.0 m/s (rápida) com variações
   - Mudanças frequentes de direção (30% chance)
   - Pausas de 5-15 segundos ocasionalmente

3. **`stationary`** (Pessoa Parada)
   - Movimentos mínimos (2 metros ocasionalmente)
   - Simula pessoa em casa, escritório, etc.

### Configurações por Pessoa

| UUID | Local | Padrão | Velocidade | Raio de Movimento |
|------|-------|--------|------------|-------------------|
| victim-001 | São Paulo | walking | normal | 500m |
| aggressor-001 | São Paulo | erratic | fast | 300m |
| victim-002 | Rio de Janeiro | walking | slow | 400m |
| aggressor-002 | Rio de Janeiro | walking | normal | 600m |
| victim-003 | Porto Alegre | stationary | slow | 100m |
| aggressor-003 | Porto Alegre | walking | normal | 400m |
| victim-004 | Belo Horizonte | walking | normal | 350m |
| aggressor-004 | Belo Horizonte | erratic | fast | 250m |
| victim-005 | Recife | walking | normal | 450m |
| aggressor-005 | Recife | walking | normal | 500m |
| victim-006 | Brasília | walking | slow | 300m |
| aggressor-006 | Brasília | erratic | fast | 200m |
| victim-007 | Fortaleza | walking | normal | 400m |
| aggressor-007 | Fortaleza | walking | normal | 350m |

## Testando o Mock

### 1. Verificar Variável de Ambiente
```bash
# No seu .env
echo $NEXT_PUBLIC_USE_MOCK_DATA
# Deve retornar: true
```

### 2. Executar Aplicação
```bash
npm run dev
```

### 3. Testar no Browser
1. Acesse `http://localhost:3000/home`
2. Clique em uma violação ativa (com `isActive: true`)
3. Observe:
   - Ícone WiFi verde no canto superior direito
   - Timestamp sendo atualizado a cada 5 segundos
   - Marcadores se movendo gradualmente no mapa

### 4. Verificar Logs no Console
Abra Developer Tools (F12) e verifique:
```
Console logs esperados:
✅ "Usando dados mock de localização (NEXT_PUBLIC_USE_MOCK_DATA=true)"
✅ "Localização atualizada para victim-001: {latitude: -23.xxxx, ...}"
✅ "Localização atualizada para aggressor-001: {latitude: -23.xxxx, ...}"
```

## Cenários de Teste Recomendados

### Teste 1: Violação em São Paulo (violation-001)
- **Vítima**: Movimento normal de caminhada
- **Agressor**: Movimento errático e rápido
- **Distância inicial**: ~150m (VIOLAÇÃO ativa)
- **Observar**: Movimento diferenciado entre vítima e agressor

### Teste 2: Violação no Rio (violation-002)  
- **Vítima**: Caminhada lenta
- **Agressor**: Caminhada normal
- **Distância inicial**: ~500m (dentro do limite)
- **Observar**: Velocidades diferentes

### Teste 3: Pessoa Estacionária (violation-003)
- **Vítima**: Praticamente parada
- **Agressor**: Caminhada normal
- **Observar**: Vítima com movimentos mínimos

## Debug e Troubleshooting

### Problema: Marcadores não se movem
**Possíveis causas:**
1. `NEXT_PUBLIC_USE_MOCK_DATA` não está `true`
2. Violação não tem `victimId` e `aggressorId`
3. Violação não está ativa (`isActive: false`)

**Solução:**
```bash
# Verificar .env
cat .env | grep NEXT_PUBLIC_USE_MOCK_DATA

# Reiniciar servidor após mudança no .env
npm run dev
```

### Problema: API não responde
**Console log esperado se funcionando:**
```
✅ 200 OK /api/location/last/victim-001
✅ 200 OK /api/location/last/aggressor-001
```

**Se aparecer erro 404/500:**
```bash
# Verificar se arquivo existe
ls src/app/api/location/last/[uuid]/route.ts

# Verificar build
npm run build
```

### Problema: Movimento muito rápido/lento
**Ajustar no arquivo mock:**
```typescript
// src/data/mock/location-tracking-mock.ts
const WALKING_SPEEDS = {
  slow: 0.4,      // diminuir valores
  normal: 0.8,    // para movimentos
  fast: 1.2,      // mais lentos
};
```

## Dados de Exemplo Simulados

### Resposta típica da API mock:
```json
{
  "success": true,
  "message": "Última localização recuperada com sucesso",
  "data": {
    "id": "location-victim-001-1704321234567",
    "latitude": -23.55067834,
    "longitude": -46.63342156,
    "timestamp": "2025-09-03T16:45:23.456Z",
    "createdAt": "2025-09-03T16:45:23.456Z"
  },
  "errors": [],
  "timestamp": "2025-09-03T16:45:23.456Z"
}
```

### Estados de movimento trackados:
- Posição atual
- Alvo de movimento
- Velocidade
- Direção
- Status de movimento (parado/andando)
- Tempo da última atualização

## Customização Avançada

### Adicionar nova pessoa:
```typescript
// Em location-tracking-mock.ts
"victim-008": {
  startLocation: { latitude: -12.9714, longitude: -38.5014 }, // Salvador
  movementPattern: 'walking',
  preferredSpeed: 'normal',
  areaRadius: 600,
},
```

### Modificar padrão de movimento:
```typescript
// Ajustar probabilidades no código
if (Math.random() < 0.1) { // 10% chance de pausar (original)
if (Math.random() < 0.05) { // 5% chance de pausar (menos pausas)
```

O mock foi projetado para simular comportamento humano realístico e fornecer dados consistentes para testes do frontend!