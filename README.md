## Resumo do Projeto Protetor Web

O **Protetor** é uma solução tecnológica integrada para monitoramento e fiscalização em tempo real de medidas protetivas da Lei Maria <PERSON>ha, composta por aplicativo móvel para vítimas, dispositivo de rastreamento para agressores e plataforma web de gestão. **Este projeto foca especificamente na Plataforma Web**, que serve como centro de controle para cadastro de vítimas, dispositivos de rastreio, agressores e monitoramento/fiscalização das medidas protetivas. Os componentes de aplicativo móvel e dispositivo de rastreamento são tratados como projetos separados.

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
