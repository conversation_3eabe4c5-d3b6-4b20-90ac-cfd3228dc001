"use client";

import { useState, useEffect, useCallback } from "react";
import { ActiveViolationsSummaryResponse, ViolationEventResponse } from "@/data/model/violation";
import { getActiveViolations } from "@/service/violation";
import { getMockActiveViolations } from "@/data/mock/violations-mock";

interface UseViolationsReturn {
  violations: ViolationEventResponse[];
  summary: ActiveViolationsSummaryResponse | null;
  loading: boolean;
  error: string | null;
  refreshing: boolean;
  refresh: () => void;
}

const REFRESH_INTERVAL = 10000; // 10 segundos

export function useViolations(): UseViolationsReturn {
  const [violations, setViolations] = useState<ViolationEventResponse[]>([]);
  const [summary, setSummary] = useState<ActiveViolationsSummaryResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const sortViolationsByDate = useCallback((violations: ViolationEventResponse[]): ViolationEventResponse[] => {
    return violations.sort((a, b) => 
      new Date(a.startTimestamp).getTime() - new Date(b.startTimestamp).getTime()
    );
  }, []);

  const fetchViolations = useCallback(async (showRefreshIcon = false) => {
    try {
      if (showRefreshIcon) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      
      let data: ActiveViolationsSummaryResponse | null = null;
      
      // Verifica se deve usar dados mock baseado na variável de ambiente
      const useMockData = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true';
      
      if (useMockData) {
        console.info('Usando dados mock (NEXT_PUBLIC_USE_MOCK_DATA=true)');
        data = await getMockActiveViolations();
      } else {
        try {
          // Tenta primeiro usar a API real
          const apiData = await getActiveViolations();
          data = apiData || null;
        } catch (apiError) {
          console.warn('API real falhou, usando dados mock como fallback:', apiError);
          // Se a API real falhar, usa os dados mock como fallback
          data = await getMockActiveViolations();
        }
      }
      
      if (data) {
        setSummary(data);
        const sortedViolations = sortViolationsByDate(data.activeViolations);
        setViolations(sortedViolations);
        setError(null);
      } else {
        // Se mesmo o mock falhar (muito improvável), usa dados mock direto
        data = await getMockActiveViolations();
        setSummary(data);
        const sortedViolations = sortViolationsByDate(data.activeViolations);
        setViolations(sortedViolations);
        setError(null);
      }
    } catch (err) {
      console.error('Erro ao carregar violações (incluindo mock):', err);
      setError('Erro ao conectar com o servidor');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [sortViolationsByDate]);

  const refresh = useCallback(() => {
    fetchViolations(true);
  }, [fetchViolations]);

  useEffect(() => {
    fetchViolations();
    
    const interval = setInterval(() => {
      fetchViolations(true);
    }, REFRESH_INTERVAL);

    return () => clearInterval(interval);
  }, [fetchViolations]);

  return {
    violations,
    summary,
    loading,
    error,
    refreshing,
    refresh,
  };
}