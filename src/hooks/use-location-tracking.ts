"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { ViolationLocation } from "@/data/model/violation";
import { LocationResponse } from "@/data/model/location";

interface UseLocationTrackingProps {
  victimId: string;
  aggressorId: string;
  isActive: boolean;
  intervalMs?: number;
}

interface UseLocationTrackingReturn {
  victimLocation: ViolationLocation | null;
  aggressorLocation: ViolationLocation | null;
  loading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

const DEFAULT_INTERVAL_MS = 5000; // 5 segundos

export function useLocationTracking({
  victimId,
  aggressorId,
  isActive,
  intervalMs = DEFAULT_INTERVAL_MS,
}: UseLocationTrackingProps): UseLocationTrackingReturn {
  const [victimLocation, setVictimLocation] = useState<ViolationLocation | null>(null);
  const [aggressorLocation, setAggressorLocation] = useState<ViolationLocation | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchLocation = useCallback(async (uuid: string): Promise<ViolationLocation | null> => {
    try {
      const response = await fetch(`/api/location/last/${uuid}`);
      
      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }
      
      const data: LocationResponse = await response.json();
      
      if (!data.success || !data.data) {
        console.warn(`Localização não encontrada para ${uuid}:`, data.message);
        return null;
      }
      
      
      return {
        latitude: data.data.latitude,
        longitude: data.data.longitude,
      };
    } catch (err) {
      console.warn(`Erro ao buscar localização para ${uuid}:`, err);
      return null;
    }
  }, []);

  const fetchBothLocations = useCallback(async () => {
    if (!victimId || !aggressorId || !isActive) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const [newVictimLocation, newAggressorLocation] = await Promise.all([
        fetchLocation(victimId),
        fetchLocation(aggressorId),
      ]);

      // Só atualiza se pelo menos uma das localizações foi obtida com sucesso
      if (newVictimLocation || newAggressorLocation) {
        if (newVictimLocation) {
          setVictimLocation(newVictimLocation);
        }
        if (newAggressorLocation) {
          setAggressorLocation(newAggressorLocation);
        }
        setLastUpdated(new Date().toISOString());
        setError(null);
      }
    } catch (err) {
      console.error("Erro ao buscar localizações:", err);
      // Não definir error aqui para manter funcionalidade atual se ocorrer problema
      // setError("Erro ao buscar localizações atualizadas");
    } finally {
      setLoading(false);
    }
  }, [victimId, aggressorId, isActive, fetchLocation]);

  // Configurar polling
  useEffect(() => {
    if (!isActive || !victimId || !aggressorId) {
      // Limpar interval se não estiver ativo
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Fazer primeira chamada imediatamente
    fetchBothLocations();

    // Configurar polling
    intervalRef.current = setInterval(fetchBothLocations, intervalMs);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isActive, victimId, aggressorId, intervalMs, fetchBothLocations]);

  // Cleanup quando componente é desmontado
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);

  return {
    victimLocation,
    aggressorLocation,
    loading,
    error,
    lastUpdated,
  };
}