export interface ProtectiveOrder {
  id?: string | undefined;
  victimId?: string | undefined;
  aggressorId?: string | undefined;
  minimumDistanceInMeters?: number | undefined;
  isActive?: boolean | undefined;
  judicialCaseNumber?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}

export interface ProtectiveOrderDataParam {
  id?: string | undefined;
  victimId?: string | undefined;
  aggressorId?: string | undefined;
  minimumDistanceInMeters?: number | undefined;
  isActive?: boolean | undefined;
  judicialCaseNumber?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}