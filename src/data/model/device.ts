export interface Device {
  id?: string | undefined;
  targetUserId?: string | undefined;
  fcmToken?: string | undefined;
  deviceIdentifier?: string | undefined;
  deviceName?: string | undefined;
  deviceModel?: string | undefined;
  deviceBrand?: string | undefined;
  type?: string | undefined; // Android ou iOS
  deviceType?: string | undefined; // Relógio ou Celular
  osVersion?: string | undefined;
  isActive?: boolean | undefined;
  strapOpen?: boolean | undefined;
  lastSeenAt?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}