export interface ViolationLocation {
  latitude: number;
  longitude: number;
}
export interface ViolationEventResponse {
  id: string;
  protectiveOrderId: string;
  startTimestamp: string;
  victimName: string;
  aggressorName: string;
  victimId?: string;
  aggressorId?: string;
  minimumDistanceInMeters: number;
  isActive: boolean;
  judicialCaseNumber: string;
  victimStartLocation: ViolationLocation;
  victimEndLocation: ViolationLocation;
  aggressorStartLocation: ViolationLocation;
  aggressorEndLocation: ViolationLocation;
  duration: number;
}

export interface ActiveViolationsSummaryResponse {
  totalActiveViolations: number;
  totalViolationsToday: number;
  lastUpdated: string;
  activeViolations: ViolationEventResponse[];
}
