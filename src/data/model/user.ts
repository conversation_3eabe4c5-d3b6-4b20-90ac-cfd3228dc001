export interface User {
  id?: string | undefined;
  aud?: string | undefined;
  role?: string | undefined;
  email?: string | undefined;
  name?: string | undefined;
  email_confirmed_at?: Date | undefined;
  phone?: string | undefined;
  confirmed_at?: Date | undefined;
  last_sign_in_at?: Date | undefined;
  app_metadata?: any;
  user_metadata?: any;
  identities?: any;
  created_at?: Date | undefined;
  updated_at?: Date | undefined;
  is_anonymous?: boolean | undefined;

  // id: '47eeafbf-8661-4010-9654-aac8aee21a4d',
  // aud: 'authenticated',
  // role: 'authenticated',
  // email: '<EMAIL>',
  // email_confirmed_at: '2024-11-21T23:17:21.764216Z',
  // phone: '',
  // confirmed_at: '2024-11-21T23:17:21.764216Z',
  // last_sign_in_at: '2024-11-26T22:38:37.211471Z',
  // app_metadata: { provider: 'email', providers: [Array] },
  // user_metadata: {},
  // identities: null,
  // created_at: '2024-11-21T23:17:21.7378Z',
  // updated_at: '2024-12-02T17:42:35.757873Z',
  // is_anonymous: false
}
export interface UserDataParam {
  id?: string | undefined;
  email?: string | undefined;
  password?: string | undefined;
  name?: string | undefined;
  created_at?: Date | undefined;
  updated_at?: Date | undefined;
}
