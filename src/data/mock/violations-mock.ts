import { ActiveViolationsSummaryResponse, ViolationEventResponse, ViolationLocation } from "@/data/model/violation";

// Função para calcular distância entre dois pontos (Haversine)
const calculateDistance = (loc1: ViolationLocation, loc2: ViolationLocation): number => {
  const R = 6371; // Raio da Terra em km
  const dLat = ((loc2.latitude - loc1.latitude) * Math.PI) / 180;
  const dLon = ((loc2.longitude - loc1.longitude) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((loc1.latitude * Math.PI) / 180) *
      Math.cos((loc2.latitude * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c * 1000; // Retorna em metros
};

// Mock de violações com localizações reais do Brasil para teste
export const mockViolations: ViolationEventResponse[] = [
  {
    id: "violation-001",
    protectiveOrderId: "order-001",
    startTimestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 min atrás
    victimName: "Maria Silva Santos",
    aggressorName: "João Carlos Oliveira",
    victimId: "victim-001",
    aggressorId: "aggressor-001",
    minimumDistanceInMeters: 200,
    isActive: true,
    judicialCaseNumber: "0001234-56.2024.8.26.0001",
    victimStartLocation: {
      latitude: -23.5505, // São Paulo - Praça da Sé
      longitude: -46.6333,
    },
    victimEndLocation: {
      latitude: -23.5515,
      longitude: -46.6343,
    },
    aggressorStartLocation: {
      latitude: -23.5500, // APENAS 50m da vítima - VIOLAÇÃO ÓBVIA!
      longitude: -46.6330,
    },
    aggressorEndLocation: {
      latitude: -23.5485,
      longitude: -46.6313,
    },
    duration: 45,
  },
  {
    id: "violation-002", 
    protectiveOrderId: "order-002",
    startTimestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2h atrás
    victimName: "Ana Paula Costa",
    aggressorName: "Carlos Eduardo Lima",
    victimId: "victim-002",
    aggressorId: "aggressor-002",
    minimumDistanceInMeters: 500,
    isActive: false, // Não é violação - agressor está a ~600m
    judicialCaseNumber: "0002345-67.2024.8.26.0002",
    victimStartLocation: {
      latitude: -22.9068, // Rio de Janeiro - Copacabana
      longitude: -43.1729,
    },
    victimEndLocation: {
      latitude: -22.9078,
      longitude: -43.1739,
    },
    aggressorStartLocation: {
      latitude: -22.9020, // ~600m da vítima - CONFORME (seguro)
      longitude: -43.1670,
    },
    aggressorEndLocation: {
      latitude: -22.9030,
      longitude: -43.1690,
    },
    duration: 120,
  },
  {
    id: "violation-003",
    protectiveOrderId: "order-003", 
    startTimestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6h atrás
    victimName: "Patricia Ferreira",
    aggressorName: "Roberto Alves",
    victimId: "victim-003",
    aggressorId: "aggressor-003",
    minimumDistanceInMeters: 300,
    isActive: false,
    judicialCaseNumber: "0003456-78.2024.8.26.0003",
    victimStartLocation: {
      latitude: -30.0346, // Porto Alegre - Centro
      longitude: -51.2177,
    },
    victimEndLocation: {
      latitude: -30.0356,
      longitude: -51.2187,
    },
    aggressorStartLocation: {
      latitude: -30.0320, // Próximo mas dentro do limite
      longitude: -51.2150,
    },
    aggressorEndLocation: {
      latitude: -30.0330,
      longitude: -51.2160,
    },
    duration: 25,
  },
  {
    id: "violation-004",
    protectiveOrderId: "order-004",
    startTimestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 min atrás
    victimName: "Sandra Rodrigues",
    aggressorName: "Fernando Costa",
    victimId: "victim-004",
    aggressorId: "aggressor-004",
    minimumDistanceInMeters: 1000,
    isActive: true,
    judicialCaseNumber: "0004567-89.2024.8.26.0004",
    victimStartLocation: {
      latitude: -19.9191, // Belo Horizonte - Savassi
      longitude: -43.9386,
    },
    victimEndLocation: {
      latitude: -19.9201,
      longitude: -43.9396,
    },
    aggressorStartLocation: {
      latitude: -19.9150, // ~700m da vítima - VIOLAÇÃO CLARA!
      longitude: -43.9350,
    },
    aggressorEndLocation: {
      latitude: -19.9160,
      longitude: -43.9360,
    },
    duration: 90,
  },
  {
    id: "violation-005",
    protectiveOrderId: "order-005",
    startTimestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8h atrás
    victimName: "Lucia Helena Martins",
    aggressorName: "Antonio Silva",
    victimId: "victim-005",
    aggressorId: "aggressor-005",
    minimumDistanceInMeters: 250,
    isActive: false, // Não é violação - agressor está a ~300m
    judicialCaseNumber: "0005678-90.2024.8.26.0005",
    victimStartLocation: {
      latitude: -8.0476, // Recife - Centro
      longitude: -34.8770,
    },
    victimEndLocation: {
      latitude: -8.0486,
      longitude: -34.8780,
    },
    aggressorStartLocation: {
      latitude: -8.0400, // ~300m da vítima - CONFORME (seguro)
      longitude: -34.8700,
    },
    aggressorEndLocation: {
      latitude: -8.0410,
      longitude: -34.8710,
    },
    duration: 15,
  },
  {
    id: "violation-007",
    protectiveOrderId: "order-007",
    startTimestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3h atrás
    victimName: "Fernanda Lima",
    aggressorName: "Ricardo Santos",
    victimId: "victim-007",
    aggressorId: "aggressor-007",
    minimumDistanceInMeters: 400,
    isActive: false,
    judicialCaseNumber: "0007890-12.2024.8.26.0007",
    victimStartLocation: {
      latitude: -3.7319, // Fortaleza - Praia de Iracema
      longitude: -38.5267,
    },
    victimEndLocation: {
      latitude: -3.7329,
      longitude: -38.5277,
    },
    aggressorStartLocation: {
      latitude: -3.7280, // Distância segura
      longitude: -38.5220,
    },
    aggressorEndLocation: {
      latitude: -3.7290,
      longitude: -38.5230,
    },
    duration: 35,
  },
];

// Validar e corrigir status baseado na distância real
const validateAndFixViolationStatus = () => {
  mockViolations.forEach(violation => {
    const distance = calculateDistance(violation.victimStartLocation, violation.aggressorStartLocation);
    const isRealViolation = distance < violation.minimumDistanceInMeters;
    violation.isActive = isRealViolation;
  });
};

// Executar validação ao carregar o módulo
validateAndFixViolationStatus();

// Mock do resumo de violações
export const mockViolationsSummary: ActiveViolationsSummaryResponse = {
  totalActiveViolations: mockViolations.filter(v => v.isActive).length,
  totalViolationsToday: mockViolations.length,
  lastUpdated: new Date().toISOString(),
  activeViolations: mockViolations,
};

// Função para simular delay de API
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock da função de API com dados simulados
export async function getMockActiveViolations(): Promise<ActiveViolationsSummaryResponse> {
  // Simula latência de rede
  await delay(Math.random() * 1000 + 500); // 500-1500ms
  
  // Simula pequena chance de erro (5%)
  if (Math.random() < 0.05) {
    throw new Error('Erro simulado de conexão');
  }
  
  // Simula atualizações dinâmicas - ocasionalmente muda status de violações
  const dynamicViolations = mockViolations.map(violation => {
    // 10% chance de mudar status
    if (Math.random() < 0.1) {
      return {
        ...violation,
        isActive: Math.random() > 0.3, // 70% chance de ficar ativa
        startTimestamp: violation.isActive 
          ? violation.startTimestamp 
          : new Date(Date.now() - Math.random() * 60 * 60 * 1000).toISOString(),
      };
    }
    return violation;
  });
  
  return {
    totalActiveViolations: dynamicViolations.filter(v => v.isActive).length,
    totalViolationsToday: dynamicViolations.length,
    lastUpdated: new Date().toISOString(),
    activeViolations: dynamicViolations,
  };
}