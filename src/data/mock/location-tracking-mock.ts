import { ViolationLocation } from "@/data/model/violation";
import { LocationResponse } from "@/data/model/location";

interface PersonMovement {
  currentPosition: ViolationLocation;
  targetPosition: ViolationLocation;
  speed: number; // metros por segundo (velocidade de caminhada normal: 1.4 m/s)
  lastUpdate: number;
  direction: number; // ângulo em radianos
  isMoving: boolean;
  pauseUntil?: number; // timestamp para pausas
}

// Velocidades típicas em m/s
const WALKING_SPEEDS = {
  slow: 0.8,      // caminhada lenta
  normal: 1.4,    // caminhada normal
  fast: 2.0,      // caminhada rápida
  running: 4.0,   // corrida leve
};

// Estado interno para cada pessoa sendo rastreada
const movementStates: Map<string, PersonMovement> = new Map();

// Configurações iniciais baseadas nos dados mock das violações
const initialConfigurations: Record<string, {
  startLocation: ViolationLocation;
  movementPattern: 'walking' | 'stationary' | 'erratic';
  preferredSpeed: keyof typeof WALKING_SPEEDS;
  areaRadius: number; // raio da <PERSON>rea onde a pessoa se move (em metros)
}> = {
  "victim-001": {
    startLocation: { latitude: -23.5505, longitude: -46.6333 }, // São Paulo
    movementPattern: 'walking',
    preferredSpeed: 'normal',
    areaRadius: 500,
  },
  "aggressor-001": {
    startLocation: { latitude: -23.5495, longitude: -46.6323 }, // São Paulo - próximo
    movementPattern: 'erratic',
    preferredSpeed: 'fast',
    areaRadius: 300,
  },
  "victim-002": {
    startLocation: { latitude: -22.9068, longitude: -43.1729 }, // Rio de Janeiro
    movementPattern: 'walking',
    preferredSpeed: 'slow',
    areaRadius: 400,
  },
  "aggressor-002": {
    startLocation: { latitude: -22.9020, longitude: -43.1680 }, // Rio de Janeiro
    movementPattern: 'walking',
    preferredSpeed: 'normal',
    areaRadius: 600,
  },
  "victim-003": {
    startLocation: { latitude: -30.0346, longitude: -51.2177 }, // Porto Alegre
    movementPattern: 'stationary',
    preferredSpeed: 'slow',
    areaRadius: 100,
  },
  "aggressor-003": {
    startLocation: { latitude: -30.0320, longitude: -51.2150 }, // Porto Alegre
    movementPattern: 'walking',
    preferredSpeed: 'normal',
    areaRadius: 400,
  },
  "victim-004": {
    startLocation: { latitude: -19.9191, longitude: -43.9386 }, // Belo Horizonte
    movementPattern: 'walking',
    preferredSpeed: 'normal',
    areaRadius: 350,
  },
  "aggressor-004": {
    startLocation: { latitude: -19.9150, longitude: -43.9350 }, // Belo Horizonte
    movementPattern: 'erratic',
    preferredSpeed: 'fast',
    areaRadius: 250,
  },
  "victim-005": {
    startLocation: { latitude: -8.0476, longitude: -34.8770 }, // Recife
    movementPattern: 'walking',
    preferredSpeed: 'normal',
    areaRadius: 450,
  },
  "aggressor-005": {
    startLocation: { latitude: -8.0400, longitude: -34.8700 }, // Recife
    movementPattern: 'walking',
    preferredSpeed: 'normal',
    areaRadius: 500,
  },
  "victim-006": {
    startLocation: { latitude: -15.7942, longitude: -47.8822 }, // Brasília
    movementPattern: 'walking',
    preferredSpeed: 'slow',
    areaRadius: 300,
  },
  "aggressor-006": {
    startLocation: { latitude: -15.7930, longitude: -47.8810 }, // Brasília
    movementPattern: 'erratic',
    preferredSpeed: 'fast',
    areaRadius: 200,
  },
  "victim-007": {
    startLocation: { latitude: -3.7319, longitude: -38.5267 }, // Fortaleza
    movementPattern: 'walking',
    preferredSpeed: 'normal',
    areaRadius: 400,
  },
  "aggressor-007": {
    startLocation: { latitude: -3.7280, longitude: -38.5220 }, // Fortaleza
    movementPattern: 'walking',
    preferredSpeed: 'normal',
    areaRadius: 350,
  },
};

// Função para converter metros para graus (aproximação)
function metersToLatLng(meters: number, latitude: number): { latOffset: number; lngOffset: number } {
  const earthRadius = 6371000; // metros
  const latOffset = (meters / earthRadius) * (180 / Math.PI);
  const lngOffset = (meters / (earthRadius * Math.cos(latitude * Math.PI / 180))) * (180 / Math.PI);
  return { latOffset, lngOffset };
}

// Função para calcular distância entre dois pontos em metros
function calculateDistance(loc1: ViolationLocation, loc2: ViolationLocation): number {
  const R = 6371000; // metros
  const dLat = ((loc2.latitude - loc1.latitude) * Math.PI) / 180;
  const dLon = ((loc2.longitude - loc1.longitude) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((loc1.latitude * Math.PI) / 180) *
      Math.cos((loc2.latitude * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// Função para gerar novo ponto de destino dentro do raio permitido
function generateNewTarget(
  baseLocation: ViolationLocation,
  currentLocation: ViolationLocation,
  radius: number
): ViolationLocation {
  const angle = Math.random() * 2 * Math.PI;
  const distance = Math.random() * radius * 0.8 + radius * 0.2; // Entre 20% e 100% do raio
  
  const { latOffset, lngOffset } = metersToLatLng(distance, baseLocation.latitude);
  
  return {
    latitude: baseLocation.latitude + latOffset * Math.cos(angle),
    longitude: baseLocation.longitude + lngOffset * Math.sin(angle),
  };
}

// Função para simular movimento realístico
function simulateMovement(uuid: string): ViolationLocation {
  const config = initialConfigurations[uuid];
  if (!config) {
    throw new Error(`Configuração não encontrada para UUID: ${uuid}`);
  }

  let movement = movementStates.get(uuid);
  const now = Date.now();

  // Inicializar se não existe
  if (!movement) {
    movement = {
      currentPosition: { ...config.startLocation },
      targetPosition: generateNewTarget(
        config.startLocation,
        config.startLocation,
        config.areaRadius
      ),
      speed: WALKING_SPEEDS[config.preferredSpeed],
      lastUpdate: now,
      direction: 0,
      isMoving: true,
    };
    movementStates.set(uuid, movement);
  }

  // Verificar se está em pausa
  if (movement.pauseUntil && now < movement.pauseUntil) {
    return movement.currentPosition;
  }

  const deltaTime = (now - movement.lastUpdate) / 1000; // segundos
  movement.lastUpdate = now;

  // Diferentes padrões de movimento
  switch (config.movementPattern) {
    case 'stationary':
      // Movimentos muito pequenos (pessoa parada)
      if (Math.random() < 0.1) { // 10% chance de se mover um pouco
        const { latOffset, lngOffset } = metersToLatLng(2, movement.currentPosition.latitude);
        movement.currentPosition.latitude += (Math.random() - 0.5) * latOffset;
        movement.currentPosition.longitude += (Math.random() - 0.5) * lngOffset;
      }
      break;

    case 'erratic':
      // Movimento errático - muda direção frequentemente
      if (Math.random() < 0.3) { // 30% chance de mudar de direção
        movement.targetPosition = generateNewTarget(
          config.startLocation,
          movement.currentPosition,
          config.areaRadius
        );
      }
      // Velocidade variável
      movement.speed = WALKING_SPEEDS[config.preferredSpeed] * (0.5 + Math.random());
      // Pausas ocasionais
      if (Math.random() < 0.05) { // 5% chance de pausar
        movement.pauseUntil = now + (Math.random() * 10000 + 5000); // 5-15 segundos
        movement.isMoving = false;
        return movement.currentPosition;
      }
      break;

    case 'walking':
    default:
      // Movimento normal de caminhada
      if (Math.random() < 0.02) { // 2% chance de pausar
        movement.pauseUntil = now + (Math.random() * 5000 + 2000); // 2-7 segundos
        movement.isMoving = false;
        return movement.currentPosition;
      }
      break;
  }

  // Calcular movimento em direção ao alvo
  const distanceToTarget = calculateDistance(movement.currentPosition, movement.targetPosition);
  
  // Se chegou perto do alvo, gerar novo alvo
  if (distanceToTarget < 20) { // 20 metros de tolerância
    movement.targetPosition = generateNewTarget(
      config.startLocation,
      movement.currentPosition,
      config.areaRadius
    );
  }

  // Calcular movimento
  const distanceToMove = movement.speed * deltaTime; // metros por segundo
  const direction = Math.atan2(
    movement.targetPosition.longitude - movement.currentPosition.longitude,
    movement.targetPosition.latitude - movement.currentPosition.latitude
  );

  const { latOffset, lngOffset } = metersToLatLng(distanceToMove, movement.currentPosition.latitude);
  
  movement.currentPosition.latitude += latOffset * Math.cos(direction);
  movement.currentPosition.longitude += lngOffset * Math.sin(direction);
  movement.direction = direction;
  movement.isMoving = true;

  return movement.currentPosition;
}

// Função principal do mock
export async function getMockLocationData(uuid: string): Promise<LocationResponse> {
  // Simular latência de rede
  await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 100)); // 100-400ms

  // Simular pequena chance de erro (3%)
  if (Math.random() < 0.03) {
    throw new Error('Erro simulado de conexão');
  }

  if (!initialConfigurations[uuid]) {
    return {
      success: false,
      message: "Localização não encontrada para o UUID fornecido",
      data: null as any,
      errors: [`UUID ${uuid} não encontrado`],
      timestamp: new Date().toISOString(),
    };
  }

  try {
    const currentLocation = simulateMovement(uuid);
    
    return {
      success: true,
      message: "Última localização recuperada com sucesso",
      data: {
        id: `location-${uuid}-${Date.now()}`,
        latitude: Number(currentLocation.latitude.toFixed(8)),
        longitude: Number(currentLocation.longitude.toFixed(8)),
        timestamp: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      },
      errors: [],
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      success: false,
      message: "Erro ao simular movimento",
      data: null as any,
      errors: [error instanceof Error ? error.message : 'Erro desconhecido'],
      timestamp: new Date().toISOString(),
    };
  }
}

// Função para resetar estado (útil para testes)
export function resetMovementState(uuid?: string) {
  if (uuid) {
    movementStates.delete(uuid);
  } else {
    movementStates.clear();
  }
}

// Função para obter estado atual (útil para debug)
export function getMovementState(uuid: string): PersonMovement | undefined {
  return movementStates.get(uuid);
}