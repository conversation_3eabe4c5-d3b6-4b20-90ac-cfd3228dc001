'use client';

import * as React from 'react';
import * as ProgressPrimitive from '@radix-ui/react-progress';

import { cn } from '@/lib/utils';

export interface AppProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> {
  value?: number;
  variant?: 'success' | 'danger' | 'default'; // Nova prop `variant`
}

const AppProgress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  AppProgressProps
>(({ className, value, variant = 'default', ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(
      'relative h-2 w-full overflow-hidden rounded-full',
      variant === 'success' && 'bg-green-500/20', // Cor verde para sucesso
      variant === 'danger' && 'bg-red-500/20', // Cor vermelha para perigo
      variant === 'default' && 'bg-primary/20', // Cor padrão
      className
    )}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className={cn(
        'h-full w-full flex-1 transition-all',
        variant === 'success' && 'bg-green-500', // Cor verde para sucesso
        variant === 'danger' && 'bg-red-500', // Cor vermelha para perigo
        variant === 'default' && 'bg-primary' // Cor padrão
      )}
      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
    />
  </ProgressPrimitive.Root>
));
AppProgress.displayName = ProgressPrimitive.Root.displayName;

export { AppProgress };
