"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { ViolationLocation } from "@/data/model/violation";
import { useLocationTracking } from "@/hooks/use-location-tracking";
import { cn } from "@/lib/utils";
import { Alert<PERSON>riangle, Wifi } from "lucide-react";

interface GoogleMapWithTrackingProps {
  initialVictimLocation: ViolationLocation;
  initialAggressorLocation: ViolationLocation;
  victimId?: string;
  aggressorId?: string;
  isActive?: boolean;
  className?: string;
  onMapLoad?: (map: google.maps.Map) => void;
  violationId?: string;
  restrictionRadiusInMeters?: number;
}

declare global {
  interface Window {
    google: any;
    initGoogleMapWithTracking: () => void;
  }
}

export function GoogleMapWithTracking({
  initialVictimLocation,
  initialAggressorLocation,
  victimId,
  aggressorId,
  isActive = false,
  className,
  onMapLoad,
  violationId,
  restrictionRadiusInMeters,
}: GoogleMapWithTrackingProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const victimMarkerRef = useRef<google.maps.Marker | null>(null);
  const aggressorMarkerRef = useRef<google.maps.Marker | null>(null);
  const polylineRef = useRef<google.maps.Polyline | null>(null);
  const restrictionCircleRef = useRef<google.maps.Circle | null>(null);

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const [isRecenteringMap, setIsRecenteringMap] = useState(false);

  // Usar localizações atuais ou iniciais como fallback
  const {
    victimLocation: trackedVictimLocation,
    aggressorLocation: trackedAggressorLocation,
    loading: trackingLoading,
    lastUpdated,
  } = useLocationTracking({
    victimId: victimId || "",
    aggressorId: aggressorId || "",
    isActive: isActive && !!victimId && !!aggressorId,
  });

  // Determinar quais localizações usar (tracking ou iniciais)
  const currentVictimLocation = trackedVictimLocation || initialVictimLocation;
  const currentAggressorLocation =
    trackedAggressorLocation || initialAggressorLocation;

  const calculateDistance = (
    loc1: ViolationLocation,
    loc2: ViolationLocation
  ): number => {
    const R = 6371; // Raio da Terra em km
    const dLat = ((loc2.latitude - loc1.latitude) * Math.PI) / 180;
    const dLon = ((loc2.longitude - loc1.longitude) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((loc1.latitude * Math.PI) / 180) *
        Math.cos((loc2.latitude * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  const calculateOptimalZoom = (): number => {
    const distance = calculateDistance(
      currentVictimLocation,
      currentAggressorLocation
    );

    const radiusInKm = (restrictionRadiusInMeters || 0) / 1000;

    // Se o raio de restrição é pequeno, usar zoom alto para visualizar
    if (radiusInKm < 0.2) return 17; // < 200m
    if (radiusInKm < 0.5) return 16; // < 500m
    if (radiusInKm < 1) return 15; // < 1km

    // Caso contrário, usar zoom baseado na distância entre pontos
    if (distance < 0.1) return 18;
    if (distance < 0.5) return 17;
    if (distance < 1) return 15;
    if (distance < 5) return 14;
    if (distance < 10) return 13;
    return 12;
  };

  const createRestrictionCircle = (
    map: google.maps.Map,
    center: ViolationLocation,
    radiusInMeters: number
  ): google.maps.Circle => {
    const RESTRICTION_CIRCLE_CONFIG = {
      strokeColor: "#10B981",
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: "#10B981",
      fillOpacity: 0.15,
    } as const;

    return new google.maps.Circle({
      strokeColor: RESTRICTION_CIRCLE_CONFIG.strokeColor,
      strokeOpacity: RESTRICTION_CIRCLE_CONFIG.strokeOpacity,
      strokeWeight: RESTRICTION_CIRCLE_CONFIG.strokeWeight,
      fillColor: RESTRICTION_CIRCLE_CONFIG.fillColor,
      fillOpacity: RESTRICTION_CIRCLE_CONFIG.fillOpacity,
      map,
      center: { lat: center.latitude, lng: center.longitude },
      radius: radiusInMeters,
    });
  };

  const centerMapOnMarkers = useCallback(() => {
    if (!mapInstanceRef.current || !window.google) return;

    setIsRecenteringMap(true);

    // Calcular o centro entre vítima e agressor
    const centerLat =
      (initialVictimLocation.latitude + initialAggressorLocation.latitude) / 2;
    const centerLng =
      (initialVictimLocation.longitude + initialAggressorLocation.longitude) /
      2;

    // Mover o mapa para o novo centro
    mapInstanceRef.current.panTo({ lat: centerLat, lng: centerLng });

    // Ajustar zoom baseado na distância
    const optimalZoom = calculateOptimalZoom();

    // Ajustar zoom após delay para permitir que o pan complete
    setTimeout(() => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.setZoom(optimalZoom);
      }
      setIsRecenteringMap(false);
    }, 800);
  }, [initialVictimLocation, initialAggressorLocation]);

  const updateMapMarkers = () => {
    if (!mapInstanceRef.current || !window.google) return;

    // Atualizar marcador da vítima
    if (victimMarkerRef.current) {
      victimMarkerRef.current.setPosition({
        lat: currentVictimLocation.latitude,
        lng: currentVictimLocation.longitude,
      });
    }

    // Atualizar marcador do agressor
    if (aggressorMarkerRef.current) {
      aggressorMarkerRef.current.setPosition({
        lat: currentAggressorLocation.latitude,
        lng: currentAggressorLocation.longitude,
      });
    }

    // Atualizar círculo de restrição (posição)
    if (restrictionCircleRef.current) {
      restrictionCircleRef.current.setCenter({
        lat: currentVictimLocation.latitude,
        lng: currentVictimLocation.longitude,
      });
    }

    // Atualizar linha de conexão
    if (polylineRef.current) {
      polylineRef.current.setPath([
        {
          lat: currentVictimLocation.latitude,
          lng: currentVictimLocation.longitude,
        },
        {
          lat: currentAggressorLocation.latitude,
          lng: currentAggressorLocation.longitude,
        },
      ]);
    }
  };

  const initializeMap = () => {
    if (!window.google || !window.google.maps) {
      setError("Google Maps API não carregou");
      setIsLoading(false);
      return;
    }

    if (!mapRef.current) return;

    try {
      const centerLat =
        (currentVictimLocation.latitude + currentAggressorLocation.latitude) /
        2;
      const centerLng =
        (currentVictimLocation.longitude + currentAggressorLocation.longitude) /
        2;

      const mapOptions: google.maps.MapOptions = {
        center: { lat: centerLat, lng: centerLng },
        zoom: calculateOptimalZoom(),
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        zoomControl: true,
        streetViewControl: false,
        mapTypeControl: false,
        fullscreenControl: false,
        styles: [
          {
            featureType: "poi",
            stylers: [{ visibility: "off" }],
          },
          {
            featureType: "transit",
            stylers: [{ visibility: "off" }],
          },
          {
            featureType: "administrative",
            elementType: "labels",
            stylers: [{ visibility: "simplified" }],
          },
          {
            featureType: "road",
            elementType: "labels.icon",
            stylers: [{ visibility: "off" }],
          },
          {
            featureType: "landscape",
            stylers: [{ saturation: -100 }, { lightness: 60 }],
          },
          {
            featureType: "road.highway",
            elementType: "geometry.fill",
            stylers: [{ color: "#ffffff" }],
          },
          {
            featureType: "road.arterial",
            elementType: "geometry.fill",
            stylers: [{ color: "#ffffff" }],
          },
        ],
      };

      const map = new google.maps.Map(mapRef.current, mapOptions);
      mapInstanceRef.current = map;

      // Marcador da vítima (azul)
      victimMarkerRef.current = new google.maps.Marker({
        position: {
          lat: currentVictimLocation.latitude,
          lng: currentVictimLocation.longitude,
        },
        map,
        title: "Localização da Vítima",
        icon: {
          path: google.maps.SymbolPath.CIRCLE,
          fillColor: "#3B82F6",
          fillOpacity: 0.9,
          strokeColor: "#1E40AF",
          strokeWeight: 2,
          scale: 10,
        },
      });

      // Marcador do agressor (vermelho)
      aggressorMarkerRef.current = new google.maps.Marker({
        position: {
          lat: currentAggressorLocation.latitude,
          lng: currentAggressorLocation.longitude,
        },
        map,
        title: "Localização do Agressor",
        icon: {
          path: google.maps.SymbolPath.CIRCLE,
          fillColor: "#EF4444",
          fillOpacity: 0.9,
          strokeColor: "#DC2626",
          strokeWeight: 2,
          scale: 10,
        },
      });

      // Círculo de restrição na posição da vítima
      if (restrictionRadiusInMeters && restrictionRadiusInMeters > 0) {
        restrictionCircleRef.current = createRestrictionCircle(
          map,
          currentVictimLocation,
          restrictionRadiusInMeters
        );
      }

      // Linha conectando os dois pontos
      polylineRef.current = new google.maps.Polyline({
        path: [
          {
            lat: currentVictimLocation.latitude,
            lng: currentVictimLocation.longitude,
          },
          {
            lat: currentAggressorLocation.latitude,
            lng: currentAggressorLocation.longitude,
          },
        ],
        geodesic: true,
        strokeColor: "#6B7280",
        strokeOpacity: 0.8,
        strokeWeight: 2,
        map,
      });

      onMapLoad?.(map);
      setIsLoading(false);
      setError(null);
    } catch (err) {
      setError("Erro ao carregar o mapa");
      setIsLoading(false);
    }
  };

  // Efeito para atualizar visualização quando violationId mudar
  useEffect(() => {
    if (!violationId) return;

    const updateMapView = () => {
      if (
        mapInstanceRef.current &&
        window.google &&
        !isLoading &&
        scriptLoaded
      ) {
        // Reposicionar marcadores
        if (victimMarkerRef.current) {
          victimMarkerRef.current.setPosition({
            lat: initialVictimLocation.latitude,
            lng: initialVictimLocation.longitude,
          });
        }

        if (aggressorMarkerRef.current) {
          aggressorMarkerRef.current.setPosition({
            lat: initialAggressorLocation.latitude,
            lng: initialAggressorLocation.longitude,
          });
        }

        // Remover círculo anterior se existir
        if (restrictionCircleRef.current) {
          restrictionCircleRef.current.setMap(null);
          restrictionCircleRef.current = null;
        }

        // Recriar círculo de restrição com novo raio
        if (restrictionRadiusInMeters && restrictionRadiusInMeters > 0) {
          restrictionCircleRef.current = createRestrictionCircle(
            mapInstanceRef.current,
            initialVictimLocation,
            restrictionRadiusInMeters
          );
        }

        // Atualizar linha de conexão
        if (polylineRef.current) {
          polylineRef.current.setPath([
            {
              lat: initialVictimLocation.latitude,
              lng: initialVictimLocation.longitude,
            },
            {
              lat: initialAggressorLocation.latitude,
              lng: initialAggressorLocation.longitude,
            },
          ]);
        }

        // Mover visualização do mapa
        centerMapOnMarkers();
        return true;
      }
      return false;
    };

    // Tentar atualizar imediatamente ou após delay
    if (!updateMapView()) {
      const timeoutId = setTimeout(updateMapView, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [
    violationId,
    initialVictimLocation,
    initialAggressorLocation,
    restrictionRadiusInMeters,
    centerMapOnMarkers,
  ]);

  // Efeito para atualizar marcadores quando as localizações mudam (rastreamento)
  useEffect(() => {
    if (!isLoading && scriptLoaded) {
      updateMapMarkers();
    }
  }, [
    currentVictimLocation,
    currentAggressorLocation,
    isLoading,
    scriptLoaded,
  ]);

  useEffect(() => {
    if (!currentVictimLocation || !currentAggressorLocation) {
      setError("Localizações não fornecidas");
      setIsLoading(false);
      return;
    }

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      setError("Google Maps API Key não configurada");
      setIsLoading(false);
      return;
    }

    if (window.google && window.google.maps) {
      initializeMap();
      return;
    }

    const existingScript = document.querySelector(
      `script[src*="maps.googleapis.com"]`
    );
    if (existingScript && !scriptLoaded) {
      existingScript.addEventListener("load", () => {
        setScriptLoaded(true);
        initializeMap();
      });
      return;
    }

    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initGoogleMapWithTracking`;
    script.async = true;
    script.defer = true;

    window.initGoogleMapWithTracking = () => {
      setScriptLoaded(true);
      initializeMap();
    };

    script.onerror = () => {
      setError("Erro ao carregar Google Maps API");
      setIsLoading(false);
    };

    document.head.appendChild(script);

    return () => {
      if ("initGoogleMapWithTracking" in window) {
        (window as any).initGoogleMapWithTracking = undefined;
      }
    };
  }, []);

  if (error) {
    return (
      <div
        className={cn(
          "flex items-center justify-center min-h-[400px] bg-gray-50 rounded-lg border",
          className
        )}
      >
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">
            Mapa indisponível
          </h3>
          <p className="text-gray-600 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  const isTrackingActive = isActive && victimId && aggressorId;

  return (
    <div className={cn("relative", className)}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 text-sm">Carregando mapa...</p>
          </div>
        </div>
      )}

      <div
        ref={mapRef}
        className="w-full h-full min-h-[400px] rounded-lg"
        style={{ opacity: isLoading ? 0 : 1 }}
      />

      {!isLoading && !error && (
        <>
          {/* Legenda */}
          <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg border z-10">
            <h4 className="text-sm font-semibold text-gray-800 mb-2">
              Legenda
            </h4>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                <span className="text-xs text-gray-600">Vítima</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                <span className="text-xs text-gray-600">Agressor</span>
              </div>
              {restrictionRadiusInMeters && restrictionRadiusInMeters > 0 && (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-emerald-500 rounded-full opacity-30 border-2 border-emerald-500"></div>
                  <span className="text-xs text-gray-600">
                    Zona de Restrição
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Indicador de status de rastreamento */}
          {isTrackingActive && (
            <div className="absolute top-4 right-4 bg-white p-3 rounded-lg shadow-lg border z-10">
              <div className="flex items-center gap-2">
                {trackingLoading ? (
                  <div className="animate-pulse">
                    <Wifi className="w-4 h-4 text-blue-500" />
                  </div>
                ) : (
                  <Wifi className="w-4 h-4 text-green-500" />
                )}
                <div>
                  <div className="text-xs font-semibold text-gray-800">
                    Rastreamento Ativo
                  </div>
                  {lastUpdated && (
                    <div className="text-xs text-gray-500">
                      Última atualização:{" "}
                      {new Date(lastUpdated).toLocaleTimeString("pt-BR")}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Indicador de recentralização do mapa */}
          {isRecenteringMap && (
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-3 rounded-lg shadow-lg border z-20">
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm font-medium text-gray-700">
                  Centralizando mapa...
                </span>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
