"use client"

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, AlertCircle, ExternalLink, Copy } from "lucide-react";

export function ApiKeyTester() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [testing, setTesting] = useState(false);
  
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

  const runSpecificTests = async () => {
    setTesting(true);
    const results: any[] = [];

    // Teste 1: Verificar APIs ativas
    const apisToTest = [
      'maps-backend.googleapis.com',
      'static-maps.googleapis.com', 
      'street-view-image.googleapis.com',
      'maps-embed.googleapis.com'
    ];

    for (const api of apisToTest) {
      try {
        const response = await fetch(`https://${api}/maps/api/staticmap?size=1x1&key=${apiKey}`);
        results.push({
          test: `API: ${api}`,
          status: response.status,
          message: response.status === 200 ? 'Ativa' : `Erro ${response.status}`,
          details: response.statusText
        });
      } catch (error) {
        results.push({
          test: `API: ${api}`,
          status: 'error',
          message: 'Não acessível',
          details: error instanceof Error ? error.message : 'Erro desconhecido'
        });
      }
    }

    // Teste 2: Verificar cota e billing
    try {
      const response = await fetch(`https://maps.googleapis.com/maps/api/staticmap?size=1x1&center=0,0&key=${apiKey}`);
      const responseText = await response.text();
      
      results.push({
        test: 'Static Maps API',
        status: response.status,
        message: response.ok ? 'OK' : 'Erro',
        details: response.ok ? 'API funcionando' : responseText.slice(0, 100)
      });
    } catch (error) {
      results.push({
        test: 'Static Maps API',
        status: 'error', 
        message: 'Falhou',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }

    // Teste 3: JavaScript API específica
    try {
      // Remover qualquer script anterior
      const existingScript = document.querySelector(`script[src*="maps.googleapis.com"]`);
      if (existingScript) {
        existingScript.remove();
      }

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initMap`;
      
      // Criar callback temporário
      (window as any).initMap = () => {
        results.push({
          test: 'JavaScript API',
          status: 200,
          message: 'Carregou com sucesso',
          details: 'Callback executado'
        });
        setTestResults([...results]);
        delete (window as any).initMap;
      };

      // Timeout para detectar falhas
      setTimeout(() => {
        if (!results.find(r => r.test === 'JavaScript API')) {
          results.push({
            test: 'JavaScript API',
            status: 'timeout',
            message: 'Timeout ou erro',
            details: 'Não carregou em 10 segundos'
          });
          setTestResults([...results]);
        }
        setTesting(false);
      }, 10000);

      document.head.appendChild(script);
      
    } catch (error) {
      results.push({
        test: 'JavaScript API',
        status: 'error',
        message: 'Erro ao carregar',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }

    setTestResults(results.slice(0, -1)); // Não incluir o JS API ainda
  };

  const copyApiKey = () => {
    navigator.clipboard.writeText(apiKey || '');
  };

  const getStatusColor = (status: any) => {
    if (status === 200) return 'text-green-600';
    if (status >= 400) return 'text-red-600'; 
    return 'text-yellow-600';
  };

  const getStatusIcon = (status: any) => {
    if (status === 200) return <CheckCircle className="w-4 h-4 text-green-600" />;
    if (status >= 400) return <XCircle className="w-4 h-4 text-red-600" />;
    return <AlertCircle className="w-4 h-4 text-yellow-600" />;
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Teste Específico da API Key</CardTitle>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <span>Chave atual:</span>
          <code className="bg-gray-100 px-2 py-1 rounded text-xs">
            {apiKey ? `${apiKey.slice(0, 20)}...` : 'Não configurada'}
          </code>
          {apiKey && (
            <Button variant="outline" size="sm" onClick={copyApiKey}>
              <Copy className="w-3 h-3" />
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={runSpecificTests} disabled={testing || !apiKey}>
            {testing ? 'Testando...' : 'Testar API Key'}
          </Button>
          
          <Button 
            variant="outline"
            onClick={() => window.open('https://console.cloud.google.com/apis/dashboard', '_blank')}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Console APIs
          </Button>
          
          <Button 
            variant="outline"
            onClick={() => window.open(`https://console.cloud.google.com/apis/credentials`, '_blank')}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Credenciais
          </Button>
        </div>

        {testResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-medium">Resultados dos Testes:</h3>
            {testResults.map((result, index) => (
              <div key={index} className="border rounded p-3 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(result.status)}
                  <div>
                    <div className="font-medium text-sm">{result.test}</div>
                    <div className={`text-sm ${getStatusColor(result.status)}`}>
                      {result.message}
                    </div>
                    {result.details && (
                      <div className="text-xs text-gray-600 mt-1">{result.details}</div>
                    )}
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  Status: {result.status}
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">Para resolver InvalidKeyMapError:</h4>
          <ol className="text-sm text-yellow-700 space-y-2 list-decimal list-inside">
            <li>Vá para <a href="https://console.cloud.google.com/apis/library" target="_blank" className="underline font-medium">Google Cloud Console → API Library</a></li>
            <li>Procure e ative as seguintes APIs:
              <ul className="ml-6 mt-1 list-disc list-inside">
                <li><strong>Maps JavaScript API</strong> (essencial)</li>
                <li><strong>Places API</strong> (se usando places)</li>
                <li><strong>Geocoding API</strong> (recomendado)</li>
              </ul>
            </li>
            <li>Vá para <a href="https://console.cloud.google.com/apis/credentials" target="_blank" className="underline font-medium">Credenciais</a> e edite sua API key</li>
            <li>Em "Application restrictions", selecione "HTTP referrers" e adicione:
              <ul className="ml-6 mt-1 list-disc list-inside">
                <li><code>http://localhost:*/*</code></li>
                <li><code>https://localhost:*/*</code></li>
              </ul>
            </li>
            <li>Em "API restrictions", selecione "Restrict key" e marque apenas as APIs que ativou</li>
            <li>Clique em "Save" e aguarde alguns minutos para propagação</li>
          </ol>
        </div>

        <div className="p-4 bg-red-50 rounded-lg">
          <h4 className="font-medium text-red-800 mb-2">Códigos de Erro Comuns:</h4>
          <ul className="text-sm text-red-700 space-y-1">
            <li><strong>InvalidKeyMapError:</strong> API key inválida ou APIs não ativadas</li>
            <li><strong>Status 403:</strong> API key sem permissões ou cota excedida</li>
            <li><strong>Status 400:</strong> Parâmetros inválidos na requisição</li>
            <li><strong>RefererNotAllowedMapError:</strong> Domínio não autorizado nas restrições</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}