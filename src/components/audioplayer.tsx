"use client";
import { verifyAudioWav } from "@/lib/utils";
import { useEffect, useState } from "react";

interface AudioPlayerProps {
  audioUrl: string | undefined;
}

export default function AudioPlayer({ audioUrl }: AudioPlayerProps) {
  const [url, setUrl] = useState<string | null>(null);

  function checkAudioType(audioUrl: string): string {
    const extension = audioUrl.toLowerCase().split(".").pop();

    const audioTypes = {
      mp3: "audio/mpeg",
      wav: "audio/wav",
      ogg: "audio/ogg",
    } as const;

    return audioTypes[extension as keyof typeof audioTypes] || "audio/mpeg";
  }

  const audioType = audioUrl ? checkAudioType(audioUrl) : "audio/mpeg";

  useEffect(() => {
    if (audioUrl) {
      const proxiedUrl = verifyAudioWav(audioUrl)
        ? `/api/proxy-audio?url=${encodeURIComponent(audioUrl)}`
        : audioUrl;

      fetch(proxiedUrl)
        .then(async (res) => {
          if (!res.ok) throw new Error("Erro ao carregar áudio");
          const blob = await res.blob();
          return URL.createObjectURL(blob);
        })
        .then((blobUrl) => setUrl(blobUrl))
        .catch(console.error);
    }

    return () => {
      if (url) URL.revokeObjectURL(url);
    };
  }, [audioUrl]);

  if (!url) return <div>Carregando áudio...</div>;

  return (
    <>
      {audioUrl && (
        <div className="audio-player">
          <audio controls preload="auto" controlsList="nodownload">
            <source src={url} type={audioType} />
            Seu navegador não suporta o elemento de áudio.
          </audio>
        </div>
      )}
    </>
  );
}
