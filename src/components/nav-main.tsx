'use client';

import { ChevronRight, type LucideIcon } from 'lucide-react';
import { useState, useMemo } from 'react';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import Link from 'next/link';

interface MenuItem {
  title: string;
  url: string;
  icon: LucideIcon;
  isActive?: boolean;
  items?: {
    title: string;
    url: string;
  }[];
}

interface NavMainProps {
  items: MenuItem[];
  searchTerm?: string;
}

export function NavMain({ items, searchTerm = '' }: NavMainProps) {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const filteredItems = useMemo(() => {
    if (!searchTerm) return items;

    return items
      .map((item) => {
        // Se o item principal corresponde à pesquisa, mantenha-o e todos os seus subitens
        if (item.title.toLowerCase().includes(searchTerm.toLowerCase())) {
          return item;
        }

        // Se o item tem subitens, filtre os subitens
        if (item.items?.length) {
          const filteredSubItems = item.items.filter((subItem) =>
            subItem.title.toLowerCase().includes(searchTerm.toLowerCase())
          );

          // Se encontrou subitens correspondentes, retorne o item com os subitens filtrados
          if (filteredSubItems.length > 0) {
            return {
              ...item,
              items: filteredSubItems,
            };
          }
        }

        // Se não encontrou correspondências, retorne null
        return null;
      })
      .filter(Boolean) as MenuItem[];
  }, [items, searchTerm]);

  // Expandir automaticamente itens que contêm resultados da pesquisa
  const shouldExpandItem = (item: MenuItem): boolean => {
    if (!searchTerm) return !!item.isActive;

    const itemMatches = item.title
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const subItemMatches = item.items?.some((subItem) =>
      subItem.title.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return itemMatches || !!subItemMatches;
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Menu</SidebarGroupLabel>
      <SidebarMenu>
        {filteredItems.map((item) => (
          <Collapsible
            key={item.title}
            asChild
            defaultOpen={shouldExpandItem(item)}
          >
            <SidebarMenuItem>
              {item.items?.length ? (
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton tooltip={item.title}>
                    <item.icon />
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </CollapsibleTrigger>
              ) : (
                <SidebarMenuButton asChild tooltip={item.title}>
                  <Link href={item.url}>
                    <item.icon />
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              )}
              {item.items?.length ? (
                <>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuAction className='data-[state=open]:rotate-90'>
                      <ChevronRight />
                      <span className='sr-only'>Toggle</span>
                    </SidebarMenuAction>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items?.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild>
                            <Link href={subItem.url}>
                              <span>{subItem.title}</span>
                            </Link>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </>
              ) : null}
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
