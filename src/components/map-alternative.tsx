"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ViolationEventResponse } from "@/data/model/violation";
import { ChevronDown, ExternalLink, MapPin } from "lucide-react";
import { useState } from "react";

interface MapAlternativeProps {
  violation: ViolationEventResponse;
}

export function MapAlternative({ violation }: MapAlternativeProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  const googleMapsUrl = `https://www.google.com/maps/dir/${violation.victimStartLocation.latitude},${violation.victimStartLocation.longitude}/${violation.aggressorStartLocation.latitude},${violation.aggressorStartLocation.longitude}`;

  const copyCoordinates = () => {
    const coordinates = `Vítima: ${violation.victimStartLocation.latitude.toFixed(6)}, ${violation.victimStartLocation.longitude.toFixed(6)}\nAgressor: ${violation.aggressorStartLocation.latitude.toFixed(6)}, ${violation.aggressorStartLocation.longitude.toFixed(6)}`;
    navigator.clipboard.writeText(coordinates);
    
    // Feedback visual simples
    const button = document.activeElement as HTMLButtonElement;
    if (button) {
      const originalText = button.textContent;
      button.textContent = 'Copiado!';
      setTimeout(() => {
        if (button.textContent === 'Copiado!') {
          button.textContent = originalText;
        }
      }, 2000);
    }
  };

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm"
          className="w-full justify-between p-0 h-auto hover:bg-transparent"
        >
          <h4 className="text-sm font-medium text-gray-800">Alternativas de Visualização</h4>
          <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
        </Button>
      </CollapsibleTrigger>
      
      <CollapsibleContent className="space-y-3 pt-3">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => window.open(googleMapsUrl, '_blank')}
            className="justify-start"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Abrir no Google Maps
          </Button>
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={copyCoordinates}
            className="justify-start"
          >
            <MapPin className="w-4 h-4 mr-2" />
            Copiar Coordenadas
          </Button>
        </div>

        <div className="text-xs text-gray-600 grid grid-cols-2 gap-4">
          <div>
            <span className="font-medium">Vítima:</span><br />
            {violation.victimStartLocation.latitude.toFixed(6)}, {violation.victimStartLocation.longitude.toFixed(6)}
          </div>
          <div>
            <span className="font-medium">Agressor:</span><br />
            {violation.aggressorStartLocation.latitude.toFixed(6)}, {violation.aggressorStartLocation.longitude.toFixed(6)}
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}