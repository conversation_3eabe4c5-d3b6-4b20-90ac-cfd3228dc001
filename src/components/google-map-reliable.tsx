"use client"

import { useEffect, useRef, useState } from "react";
import { ViolationLocation } from "@/data/model/violation";
import { cn } from "@/lib/utils";
import { MapPin, AlertTriangle } from "lucide-react";

interface GoogleMapReliableProps {
  victimLocation: ViolationLocation;
  aggressorLocation: ViolationLocation;
  restrictionRadiusInMeters?: number;
  className?: string;
  onMapLoad?: (map: google.maps.Map) => void;
}

declare global {
  interface Window {
    google: any;
    initGoogleMapReliable: () => void;
  }
}

export function GoogleMapReliable({
  victimLocation,
  aggressorLocation,
  restrictionRadiusInMeters,
  className,
  onMapLoad,
}: GoogleMapReliableProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [scriptLoaded, setScriptLoaded] = useState(false);

  const calculateDistance = (
    loc1: ViolationLocation,
    loc2: ViolationLocation
  ): number => {
    const R = 6371; // Raio da Terra em km
    const dLat = ((loc2.latitude - loc1.latitude) * Math.PI) / 180;
    const dLon = ((loc2.longitude - loc1.longitude) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((loc1.latitude * Math.PI) / 180) *
        Math.cos((loc2.latitude * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  const calculateOptimalZoom = (): number => {
    const distance = calculateDistance(victimLocation, aggressorLocation);
    
    if (distance < 0.1) return 18;
    if (distance < 0.5) return 16;
    if (distance < 1) return 15;
    if (distance < 5) return 13;
    if (distance < 10) return 12;
    return 11;
  };

  const createRestrictionCircle = (
    map: google.maps.Map,
    center: ViolationLocation,
    radiusInMeters: number
  ): google.maps.Circle => {
    const RESTRICTION_CIRCLE_CONFIG = {
      strokeColor: '#10B981',
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: '#10B981',
      fillOpacity: 0.15,
    } as const;

    return new google.maps.Circle({
      strokeColor: RESTRICTION_CIRCLE_CONFIG.strokeColor,
      strokeOpacity: RESTRICTION_CIRCLE_CONFIG.strokeOpacity,
      strokeWeight: RESTRICTION_CIRCLE_CONFIG.strokeWeight,
      fillColor: RESTRICTION_CIRCLE_CONFIG.fillColor,
      fillOpacity: RESTRICTION_CIRCLE_CONFIG.fillOpacity,
      map,
      center: { lat: center.latitude, lng: center.longitude },
      radius: radiusInMeters,
    });
  };

  const initializeMap = () => {
    if (!window.google || !window.google.maps) {
      setError("Google Maps API não carregou");
      setIsLoading(false);
      return;
    }

    if (!mapRef.current) return;

    try {
      const centerLat = (victimLocation.latitude + aggressorLocation.latitude) / 2;
      const centerLng = (victimLocation.longitude + aggressorLocation.longitude) / 2;

      const mapOptions: google.maps.MapOptions = {
        center: { lat: centerLat, lng: centerLng },
        zoom: calculateOptimalZoom(),
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        zoomControl: true,
        streetViewControl: false,
        mapTypeControl: false,
        fullscreenControl: false,
        styles: [
          {
            featureType: "poi",
            stylers: [{ visibility: "off" }]
          },
          {
            featureType: "transit",
            stylers: [{ visibility: "off" }]
          },
          {
            featureType: "administrative",
            elementType: "labels",
            stylers: [{ visibility: "simplified" }]
          },
          {
            featureType: "road",
            elementType: "labels.icon",
            stylers: [{ visibility: "off" }]
          },
          {
            featureType: "landscape",
            stylers: [{ saturation: -100 }, { lightness: 60 }]
          },
          {
            featureType: "road.highway",
            elementType: "geometry.fill",
            stylers: [{ color: "#ffffff" }]
          },
          {
            featureType: "road.arterial",
            elementType: "geometry.fill",
            stylers: [{ color: "#ffffff" }]
          }
        ]
      };

      const map = new google.maps.Map(mapRef.current, mapOptions);
      mapInstanceRef.current = map;

      // Marcador da vítima (azul)
      new google.maps.Marker({
        position: { lat: victimLocation.latitude, lng: victimLocation.longitude },
        map,
        title: "Localização da Vítima",
        icon: {
          path: google.maps.SymbolPath.CIRCLE,
          fillColor: "#3B82F6",
          fillOpacity: 0.9,
          strokeColor: "#1E40AF",
          strokeWeight: 2,
          scale: 10,
        },
      });

      // Marcador do agressor (vermelho)
      new google.maps.Marker({
        position: { lat: aggressorLocation.latitude, lng: aggressorLocation.longitude },
        map,
        title: "Localização do Agressor",
        icon: {
          path: google.maps.SymbolPath.CIRCLE,
          fillColor: "#EF4444",
          fillOpacity: 0.9,
          strokeColor: "#DC2626",
          strokeWeight: 2,
          scale: 10,
        },
      });

      // Círculo de restrição na posição da vítima
      if (restrictionRadiusInMeters && restrictionRadiusInMeters > 0) {
        createRestrictionCircle(map, victimLocation, restrictionRadiusInMeters);
      }

      // Linha conectando os dois pontos
      const distance = calculateDistance(victimLocation, aggressorLocation);
      new google.maps.Polyline({
        path: [
          { lat: victimLocation.latitude, lng: victimLocation.longitude },
          { lat: aggressorLocation.latitude, lng: aggressorLocation.longitude },
        ],
        geodesic: true,
        strokeColor: "#6B7280",
        strokeOpacity: 0.8,
        strokeWeight: 2,
        map,
      });

      onMapLoad?.(map);
      setIsLoading(false);
      setError(null);

    } catch (err) {
      setError("Erro ao carregar o mapa");
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!victimLocation || !aggressorLocation) {
      setError("Localizações não fornecidas");
      setIsLoading(false);
      return;
    }

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      setError("Google Maps API Key não configurada");
      setIsLoading(false);
      return;
    }

    if (window.google && window.google.maps) {
      initializeMap();
      return;
    }

    const existingScript = document.querySelector(`script[src*="maps.googleapis.com"]`);
    if (existingScript && !scriptLoaded) {
      existingScript.addEventListener('load', () => {
        setScriptLoaded(true);
        initializeMap();
      });
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initGoogleMapReliable`;
    script.async = true;
    script.defer = true;

    window.initGoogleMapReliable = () => {
      setScriptLoaded(true);
      initializeMap();
    };

    script.onerror = () => {
      setError("Erro ao carregar Google Maps API");
      setIsLoading(false);
    };

    document.head.appendChild(script);

    return () => {
      if ('initGoogleMapReliable' in window) {
        (window as any).initGoogleMapReliable = undefined;
      }
    };
  }, [victimLocation, aggressorLocation]);

  if (error) {
    return (
      <div className={cn("flex items-center justify-center min-h-[400px] bg-gray-50 rounded-lg border", className)}>
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">Mapa indisponível</h3>
          <p className="text-gray-600 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("relative", className)}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 text-sm">Carregando mapa...</p>
          </div>
        </div>
      )}
      
      <div
        ref={mapRef}
        className="w-full h-full min-h-[400px] rounded-lg"
        style={{ opacity: isLoading ? 0 : 1 }}
      />
      
      {!isLoading && !error && (
        <div className="absolute bottom-4 left-4 bg-white p-3 rounded-lg shadow-lg border z-10">
          <h4 className="text-sm font-semibold text-gray-800 mb-2">Legenda</h4>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
              <span className="text-xs text-gray-600">Vítima</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-500 rounded-full"></div>
              <span className="text-xs text-gray-600">Agressor</span>
            </div>
            {restrictionRadiusInMeters && restrictionRadiusInMeters > 0 && (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-emerald-500 rounded-full opacity-30 border-2 border-emerald-500"></div>
                <span className="text-xs text-gray-600">Zona de Restrição</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}