"use client";

import { useState, useEffect } from "react";
import { User } from "@/data/model/user";
import { getUsersByRole } from "@/service/user";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface UserSelectProps {
  role: string;
  placeholder?: string;
  value?: string;
  onValueChange?: (value: string, user?: User) => void;
  disabled?: boolean;
}

export function UserSelect({
  role,
  placeholder = "Selecione um usuário...",
  value,
  onValueChange,
  disabled = false,
}: UserSelectProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadUsers = async () => {
      setLoading(true);
      try {
        const userList = await getUsersByRole(role);
        setUsers(userList);
      } catch (error) {
        console.error("Erro ao carregar usuários:", error);
        setUsers([]);
      } finally {
        setLoading(false);
      }
    };

    loadUsers();
  }, [role]);

  const handleValueChange = (userId: string) => {
    const selectedUser = users.find((user) => user.id === userId);
    onValueChange?.(userId, selectedUser);
  };

  return (
    <Select
      value={value}
      onValueChange={handleValueChange}
      disabled={disabled || loading}
    >
      <SelectTrigger>
        <SelectValue placeholder={loading ? "Carregando..." : placeholder} />
      </SelectTrigger>
      <SelectContent>
        {users.map((user) => (
          <SelectItem key={user.id} value={user.id || ""}>
            {user.name ||
              user.user_metadata?.name ||
              user.email ||
              "Usuário sem nome"}
          </SelectItem>
        ))}
        {users.length === 0 && !loading && (
          <SelectItem value="" disabled>
            Nenhum usuário encontrado para o papel {role}
          </SelectItem>
        )}
      </SelectContent>
    </Select>
  );
}
