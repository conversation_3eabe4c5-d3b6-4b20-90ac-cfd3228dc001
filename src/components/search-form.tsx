'use client';

import { Search } from 'lucide-react';
import { useState } from 'react';

import { Label } from '@/components/ui/label';
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarInput,
  useSidebar,
} from '@/components/ui/sidebar';

interface SearchFormProps extends React.ComponentProps<'form'> {
  onSearch?: (term: string) => void;
}

export function SearchForm({ onSearch, ...props }: SearchFormProps) {
  const { state, open } = useSidebar();
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch?.(value);
  };

  return (
    <>
      {open && (
        <form {...props}>
          <SidebarGroup className='py-0'>
            <SidebarGroupContent className='relative'>
              <Label htmlFor='search' className='sr-only'>
                Search
              </Label>
              <SidebarInput
                id='search'
                value={searchTerm}
                onChange={handleSearch}
                placeholder='Pesquisar...'
                className='pl-8'
              />
              <Search className='pointer-events-none absolute left-2 top-1/2 size-4 -translate-y-1/2 select-none opacity-50' />
            </SidebarGroupContent>
          </SidebarGroup>
        </form>
      )}
    </>
  );
}
