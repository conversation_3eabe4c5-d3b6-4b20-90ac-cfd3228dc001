"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>b,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

import { usePathname } from "next/navigation";
import Link from "next/link";

const translations: { [key: string]: string } = {
  // URLs principais
  home: "Início",
  users: "Usuários",
  new: "Novo",

  // Títulos de seções
  Dashboard: "Dashboard",
  Cadastros: "Cadastros",
};

function getTranslatedText(text: string): string {
  return translations[text] || text;
}

export default function AppBreadCrumb() {
  const paths = usePathname();
  const pathNames = paths.split("/").filter((path) => path);

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {pathNames.map((link, index) => {
          let href = `/${pathNames.slice(0, index + 1).join("/")}`;
          let translatedText = getTranslatedText(link);

          // Se não encontrou tradução, capitaliza a primeira letra
          if (translatedText === link) {
            translatedText = link[0].toUpperCase() + link.slice(1, link.length);
          }

          return (
            <span className="flex gap-2.5 items-center" key={index}>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink asChild>
                  <Link href={href}>{translatedText}</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>

              {pathNames.length !== index + 1 && (
                <BreadcrumbSeparator className="hidden md:block" />
              )}
            </span>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
