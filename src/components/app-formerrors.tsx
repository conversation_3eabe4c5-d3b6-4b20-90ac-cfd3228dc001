import React from 'react';
import { FieldErrors, FieldValues } from 'react-hook-form';

interface FormErrorsProps<T extends FieldValues> {
  errors: FieldErrors<T>; // Tipagem genérica para refletir a estrutura do formulário
}

const FormErrors = <T extends FieldValues>({ errors }: FormErrorsProps<T>) => {
  if (!errors || Object.keys(errors).length === 0) return null;

  return (
    <div className='bg-red-100 text-red-700 p-3 rounded'>
      <ul>
        {Object.entries(errors).map(([key, error]) => (
          <li key={key}>{(error as any)?.message}</li>
        ))}
      </ul>
    </div>
  );
};

export default FormErrors;
