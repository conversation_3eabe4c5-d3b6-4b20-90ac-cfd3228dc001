import React, { ReactElement } from "react";
import { Badge } from "./ui/badge";

export default function AppBadgeWord({ type }: { type: string | undefined }) {
  return (
    <Badge
      className={
        type === "P"
          ? "text-xs text-[7pt] uppercase bg-red-500 hover:bg-red-600"
          : "text-xs text-[7pt] uppercase bg-green-500 hover:bg-green-600"
      }
    >
      {type === "P" ? "Proibido" : "Obrigatório"}
    </Badge>
  );
}
