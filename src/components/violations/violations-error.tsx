import { RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface ViolationsErrorProps {
  error: string;
  onRetry: () => void;
}

export function ViolationsError({ error, onRetry }: ViolationsErrorProps) {
  return (
    <div className="p-4">
      <div className="text-center py-8">
        <p className="text-red-500 mb-4">{error}</p>
        <Button onClick={onRetry} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Tentar Novamente
        </Button>
      </div>
    </div>
  );
}
