import { RefreshCw, AlertTriangle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CardTitle } from "@/components/ui/card";
import { formatDateTime } from "@/lib/utils";
import { ActiveViolationsSummaryResponse } from "@/data/model/violation";

interface ViolationsHeaderProps {
  summary: ActiveViolationsSummaryResponse | null;
  refreshing: boolean;
  onRefresh: () => void;
}

export function ViolationsHeader({
  summary,
  refreshing,
  onRefresh,
}: ViolationsHeaderProps) {
  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <ViolationsSummaryBadge
            totalActiveViolations={summary?.totalActiveViolations || 0}
          />
          <RefreshButton refreshing={refreshing} onRefresh={onRefresh} />
        </div>
      </div>
      {summary && <LastUpdatedInfo summary={summary} />}
    </>
  );
}

function ViolationsSummaryBadge({
  totalActiveViolations,
}: {
  totalActiveViolations: number;
}) {
  return (
    <Badge variant="destructive">
      {totalActiveViolations} ativa{totalActiveViolations !== 1 ? "s" : ""}
    </Badge>
  );
}

function RefreshButton({
  refreshing,
  onRefresh,
}: {
  refreshing: boolean;
  onRefresh: () => void;
}) {
  return (
    <Button
      onClick={onRefresh}
      variant="outline"
      size="sm"
      disabled={refreshing}
    >
      <RefreshCw className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`} />
    </Button>
  );
}

function LastUpdatedInfo({
  summary,
}: {
  summary: ActiveViolationsSummaryResponse;
}) {
  return (
    <div className="text-sm text-muted-foreground">
      {summary.totalViolationsToday} violações hoje • Última atualização:{" "}
      {formatDateTime(summary.lastUpdated)}
    </div>
  );
}
