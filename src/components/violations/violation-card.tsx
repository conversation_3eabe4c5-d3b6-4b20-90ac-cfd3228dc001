import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ViolationEventResponse } from "@/data/model/violation";
import { formatDateTime } from "@/lib/utils";
import { MapPin } from "lucide-react";

interface ViolationCardProps {
  violation: ViolationEventResponse;
  onViewMap?: (violation: ViolationEventResponse) => void;
  isSelected?: boolean;
}

export function ViolationCard({
  violation,
  onViewMap,
  isSelected,
}: ViolationCardProps) {
  const hasValidLocations =
    violation.victimStartLocation &&
    violation.aggressorStartLocation &&
    violation.victimStartLocation.latitude !== 0 &&
    violation.victimStartLocation.longitude !== 0 &&
    violation.aggressorStartLocation.latitude !== 0 &&
    violation.aggressorStartLocation.longitude !== 0;

  return (
    <div
      className={`border rounded-lg p-4 transition-all cursor-pointer ${
        isSelected
          ? "bg-red-200 border-red-600 shadow-md"
          : "bg-red-50 border-red-200 hover:bg-red-100"
      }`}
      onClick={() => onViewMap?.(violation)}
    >
      {/* Linha superior com botão e badge */}
      <div className="flex items-center justify-between mb-3">
        <ViolationStatus isActive={violation.isActive} />
        {hasValidLocations && (
          <Button
            variant={isSelected ? "destructive" : "outline"}
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onViewMap?.(violation);
            }}
          >
            <MapPin className="w-4 h-4 mr-2" />
            {isSelected ? "Selecionado" : "Ver Mapa"}
          </Button>
        )}
      </div>
      
      {/* Conteúdo principal */}
      <ViolationInfo violation={violation} isSelected={isSelected} />
    </div>
  );
}

function ViolationInfo({
  violation,
  isSelected,
}: {
  violation: ViolationEventResponse;
  isSelected?: boolean;
}) {
  const titleColor = isSelected ? "text-red-900" : "text-gray-900";
  const textColor = isSelected ? "text-red-800" : "text-gray-700";

  return (
    <div className="flex-1">
      <div className={`font-semibold ${titleColor}`}>
        <span className="font-medium text-sm">Vítima:</span>{" "}
        {violation.victimName}
      </div>
      <div className={`text-sm ${textColor} mt-1`}>
        <span className="font-medium">Agressor:</span> {violation.aggressorName}
      </div>
      <div className={`text-sm ${textColor}`}>
        <span className="font-medium">Distância mínima:</span>{" "}
        {violation.minimumDistanceInMeters}m
      </div>
      <div className={`text-sm ${textColor}`}>
        <span className="font-medium">Início:</span>{" "}
        {formatDateTime(violation.startTimestamp)}
      </div>
    </div>
  );
}

function ViolationStatus({ isActive }: { isActive: boolean }) {
  return (
    <Badge variant={isActive ? "destructive" : "secondary"}>
      {isActive ? "Ativa" : "Inativa"}
    </Badge>
  );
}
