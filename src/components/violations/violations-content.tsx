import { ViolationEventResponse } from "@/data/model/violation";
import { ViolationCard } from "./violation-card";

interface ViolationsContentProps {
  violations: ViolationEventResponse[];
  onSelectViolation?: (violation: ViolationEventResponse) => void;
  selectedViolationId?: string;
}

export function ViolationsContent({ 
  violations, 
  onSelectViolation, 
  selectedViolationId 
}: ViolationsContentProps) {
  if (violations.length === 0) {
    return <EmptyViolations />;
  }

  return (
    <div className="space-y-4">
      {violations.map((violation) => (
        <ViolationCard 
          key={violation.id} 
          violation={violation}
          onViewMap={onSelectViolation}
          isSelected={selectedViolationId === violation.id}
        />
      ))}
    </div>
  );
}

function EmptyViolations() {
  return (
    <div className="text-center py-8 text-muted-foreground">
      Nenhuma violação ativa encontrada
    </div>
  );
}