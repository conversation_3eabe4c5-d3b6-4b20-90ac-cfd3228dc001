import * as React from 'react';
import { useEffect, useState } from 'react';
import { NavUser } from '@/components/nav-user';
import { Icons } from './ui/icons';
import { useAuth } from '@/hooks/use-auth';

interface User {
  name: string;
  email: string;
  avatar: string;
}

export default function AppNavUser() {
  const [user, setUser] = useState<User | null>(null);
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      const token = localStorage.getItem('accessToken');
      if (token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const email = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'] || 
                       payload['email'] || 
                       'usuá****************';
          const name = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'] || 
                      payload['name'] || 
                      'Usuário';
          
          setUser({
            name,
            email,
            avatar: '',
          });
        } catch (error) {
          console.error('Erro ao decodificar token:', error);
          setUser({
            name: 'Usuário',
            email: 'usuá****************', 
            avatar: '',
          });
        }
      }
    }
  }, [isAuthenticated]);

  if (isLoading) {
    return (
      <span className='flex items-center justify-center'>
        <Icons.spinner className='mr-2 h-4 w-4 animate-spin' />
      </span>
    );
  }

  if (!user) {
    return null;
  }

  return <NavUser user={user} />;
}
