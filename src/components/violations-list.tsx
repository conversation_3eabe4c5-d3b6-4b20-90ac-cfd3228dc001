"use client";

import React from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { useViolations } from "@/hooks/use-violations";
import { ViolationsHeader } from "@/components/violations/violations-header";
import { ViolationsContent } from "@/components/violations/violations-content";
import { ViolationsLoading } from "@/components/violations/violations-loading";
import { ViolationsError } from "@/components/violations/violations-error";
import { ViolationEventResponse } from "@/data/model/violation";

interface ViolationsListProps {
  onSelectViolation?: (violation: ViolationEventResponse) => void;
  selectedViolationId?: string;
}

export function ViolationsList({
  onSelectViolation,
  selectedViolationId,
}: ViolationsListProps) {
  const { violations, summary, loading, error, refreshing, refresh } =
    useViolations();

  if (loading) {
    return <ViolationsLoading />;
  }

  if (error) {
    return <ViolationsError error={error} onRetry={refresh} />;
  }

  return (
    <div>
      <div className="p-4 border-b bg-gray-50">
        <ViolationsHeader
          summary={summary}
          refreshing={refreshing}
          onRefresh={refresh}
        />
      </div>
      <div className="p-4">
        <ViolationsContent
          violations={violations}
          onSelectViolation={onSelectViolation}
          selectedViolationId={selectedViolationId}
        />
      </div>
    </div>
  );
}
