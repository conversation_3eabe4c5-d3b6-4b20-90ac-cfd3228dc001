"use client";

import { useState } from "react";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Bell } from "lucide-react";
import {
  SidebarMenu,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import { Badge } from "./ui/badge";

export default function AppRealtimeSidebar() {
  const [open, setOpen] = useState(false);

  return (
    <div>
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>
          <SidebarMenu>
            <SidebarMenuButton tooltip="Notificações" className="p-2">
              <Bell className="size-4" />
              <div className="flex justify-between w-full">
                <div>Notificações</div>
                <Badge variant="outline">0</Badge>
              </div>
            </SidebarMenuButton>
          </SidebarMenu>
        </DrawerTrigger>
        <DrawerContent className="p-4">
          <DrawerHeader>
            <DrawerTitle>Notificações</DrawerTitle>
            <DrawerDescription>Nenhuma notificação disponível</DrawerDescription>
          </DrawerHeader>
          <p className="text-gray-500">
            As notificações em tempo real não estão disponíveis no momento.
          </p>
        </DrawerContent>
      </Drawer>
    </div>
  );
}
