import { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface PageLayoutProps {
  children: ReactNode;
  className?: string;
  title?: string;
  description?: string;
  actions?: ReactNode;
}

export function PageLayout({ 
  children, 
  className, 
  title, 
  description, 
  actions 
}: PageLayoutProps) {
  return (
    <div className={cn("flex flex-1 flex-col space-y-6 p-6 md:p-8", className)}>
      {(title || actions) && (
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            {title && (
              <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
            )}
            {description && (
              <p className="text-muted-foreground">{description}</p>
            )}
          </div>
          {actions && <div className="flex items-center space-x-2">{actions}</div>}
        </div>
      )}
      <div className="flex-1">{children}</div>
    </div>
  );
}