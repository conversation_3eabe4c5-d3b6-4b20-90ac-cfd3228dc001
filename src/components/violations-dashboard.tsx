"use client";

import { useState } from "react";
import { ViolationsList } from "@/components/violations-list";
import { GoogleMapWithTracking } from "@/components/google-map-with-tracking";
import { MapAlternative } from "@/components/map-alternative";
import { ViolationEventResponse } from "@/data/model/violation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MapPin, Users, AlertTriangle, Settings } from "lucide-react";

export function ViolationsDashboard() {
  const [selectedViolation, setSelectedViolation] =
    useState<ViolationEventResponse | null>(null);
  const [showMapDiagnostic, setShowMapDiagnostic] = useState(false);

  const handleViolationSelect = (violation: ViolationEventResponse) => {
    setSelectedViolation(violation);
  };

  return (
    <div className="relative h-full w-full overflow-hidden">
      {/* Mapa em tela cheia */}
      <div className="absolute inset-0">
        <MapSection selectedViolation={selectedViolation} />
      </div>

      {/* Lista de violações sobreposta à direita */}
      <div className="absolute top-4 right-4 bottom-4 w-96 bg-white rounded-lg shadow-lg border overflow-hidden flex flex-col">
        <div className="p-4 border-b bg-gray-50">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <h2 className="font-semibold">Violações de Medidas Protetivas</h2>
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          <ViolationsList
            onSelectViolation={handleViolationSelect}
            selectedViolationId={selectedViolation?.id}
          />
        </div>
      </div>
    </div>
  );
}

interface MapSectionProps {
  selectedViolation: ViolationEventResponse | null;
}

function MapSection({ selectedViolation }: MapSectionProps) {
  if (!selectedViolation) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-600 mb-2">
            Selecione uma Violação
          </h3>
          <p className="text-gray-500 max-w-sm">
            Clique em uma violação da lista para visualizar as posições da
            vítima e do agressor no mapa
          </p>
        </div>
      </div>
    );
  }

  const hasValidLocations =
    selectedViolation.victimStartLocation &&
    selectedViolation.aggressorStartLocation &&
    selectedViolation.victimStartLocation.latitude !== 0 &&
    selectedViolation.victimStartLocation.longitude !== 0 &&
    selectedViolation.aggressorStartLocation.latitude !== 0 &&
    selectedViolation.aggressorStartLocation.longitude !== 0;

  if (!hasValidLocations) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <AlertTriangle className="w-16 h-16 text-amber-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-600 mb-2">
            Localização Indisponível
          </h3>
          <p className="text-gray-500 max-w-sm">
            Esta violação não possui dados de localização válidos para exibir no
            mapa
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      <GoogleMapWithTracking
        violationId={selectedViolation.id}
        initialVictimLocation={selectedViolation.victimStartLocation}
        initialAggressorLocation={selectedViolation.aggressorStartLocation}
        victimId={selectedViolation.victimId}
        aggressorId={selectedViolation.aggressorId}
        isActive={selectedViolation.isActive}
        restrictionRadiusInMeters={selectedViolation.minimumDistanceInMeters}
        className="h-full w-full"
      />

      {/* Informações da violação sobreposta no mapa */}
      <div className="absolute top-4 left-4 bg-white p-4 rounded-lg shadow-lg border max-w-md">
        <div className="flex items-center gap-2 mb-2">
          <MapPin className="w-5 h-5 text-gray-600" />
          <span className="font-semibold text-gray-900">
            Violação Selecionada
          </span>
        </div>
        <div className="text-sm text-gray-700 space-y-1">
          <div>
            <Users className="w-4 h-4 inline mr-1" />
            {selectedViolation.victimName} vs {selectedViolation.aggressorName}
          </div>
          <div className="text-xs text-gray-500 mb-3">
            {selectedViolation.judicialCaseNumber}
          </div>
          <ViolationDetails violation={selectedViolation} />
          <div className="pt-3 border-t">
            <MapAlternative violation={selectedViolation} />
          </div>
        </div>
      </div>
    </div>
  );
}

function ViolationDetails({
  violation,
}: {
  violation: ViolationEventResponse;
}) {
  const calculateDistance = (
    loc1: { latitude: number; longitude: number },
    loc2: { latitude: number; longitude: number }
  ): number => {
    const R = 6371;
    const dLat = ((loc2.latitude - loc1.latitude) * Math.PI) / 180;
    const dLon = ((loc2.longitude - loc1.longitude) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((loc1.latitude * Math.PI) / 180) *
        Math.cos((loc2.latitude * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c * 1000; // Retorna em metros
  };

  // Usar localizações iniciais como base para cálculo de distância
  // O componente de rastreamento irá atualizar o mapa, mas mantemos o cálculo simples aqui
  const distance = calculateDistance(
    violation.victimStartLocation,
    violation.aggressorStartLocation
  );

  const isViolatingDistance = distance < violation.minimumDistanceInMeters;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
      <div className="text-center">
        <div className="text-gray-500">Distância Atual</div>
        <div
          className={`font-semibold ${
            isViolatingDistance ? "text-red-600" : "text-green-600"
          }`}
        >
          {distance < 1000
            ? `${Math.round(distance)}m`
            : `${(distance / 1000).toFixed(2)}km`}
        </div>
      </div>
      <div className="text-center">
        <div className="text-gray-500">Distância Mínima</div>
        <div className="font-semibold text-gray-800">
          {violation.minimumDistanceInMeters}m
        </div>
      </div>
      <div className="text-center">
        <div className="text-gray-500">Status</div>
        <div
          className={`font-semibold ${
            isViolatingDistance ? "text-red-600" : "text-green-600"
          }`}
        >
          {isViolatingDistance ? "VIOLAÇÃO" : "CONFORME"}
        </div>
      </div>
    </div>
  );
}
