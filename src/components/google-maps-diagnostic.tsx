"use client"

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, AlertCircle, ExternalLink } from "lucide-react";

interface DiagnosticResult {
  step: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: string;
}

export function GoogleMapsDiagnostic() {
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [testing, setTesting] = useState(false);

  const runDiagnostic = async () => {
    setTesting(true);
    const diagnosticResults: DiagnosticResult[] = [];

    // 1. Verificar se a API key existe
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      diagnosticResults.push({
        step: "Verificação da API Key",
        status: 'error',
        message: "API Key não encontrada",
        details: "Configure NEXT_PUBLIC_GOOGLE_MAPS_API_KEY no arquivo .env.local"
      });
    } else {
      diagnosticResults.push({
        step: "Verificação da API Key",
        status: 'success',
        message: "API Key encontrada",
        details: `Primeiros 10 caracteres: ${apiKey.substring(0, 10)}...`
      });

      // 2. Verificar formato da API key
      if (!apiKey.startsWith('AIza')) {
        diagnosticResults.push({
          step: "Formato da API Key",
          status: 'warning',
          message: "Formato suspeito",
          details: "API Keys do Google Maps geralmente começam com 'AIza'"
        });
      } else if (apiKey.length < 35 || apiKey.length > 45) {
        diagnosticResults.push({
          step: "Formato da API Key", 
          status: 'warning',
          message: "Tamanho suspeito",
          details: `Tamanho atual: ${apiKey.length} caracteres. Esperado: ~39`
        });
      } else {
        diagnosticResults.push({
          step: "Formato da API Key",
          status: 'success',
          message: "Formato correto",
          details: `Tamanho: ${apiKey.length} caracteres`
        });
      }

      // 3. Testar carregamento da API
      try {
        const testScript = document.createElement('script');
        testScript.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
        
        await new Promise((resolve, reject) => {
          testScript.onload = resolve;
          testScript.onerror = reject;
          
          // Timeout de 10 segundos
          setTimeout(() => reject(new Error('Timeout')), 10000);
          
          document.head.appendChild(testScript);
        });

        diagnosticResults.push({
          step: "Carregamento da API",
          status: 'success',
          message: "API carregada com sucesso",
          details: "Script do Google Maps carregou sem erros"
        });

        // Limpar o script de teste
        document.head.removeChild(testScript);
        
      } catch (error) {
        diagnosticResults.push({
          step: "Carregamento da API",
          status: 'error',
          message: "Erro ao carregar API",
          details: `Erro: ${error instanceof Error ? error.message : 'Desconhecido'}`
        });
      }
    }

    setResults(diagnosticResults);
    setTesting(false);
  };

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-amber-600" />;
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-amber-200 bg-amber-50';
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span>Diagnóstico Google Maps API</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={runDiagnostic} disabled={testing}>
            {testing ? 'Testando...' : 'Executar Diagnóstico'}
          </Button>
          <Button
            variant="outline"
            onClick={() => window.open('https://console.cloud.google.com/apis/credentials', '_blank')}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Google Console
          </Button>
        </div>

        {results.length > 0 && (
          <div className="space-y-3">
            {results.map((result, index) => (
              <div key={index} className={`border rounded-lg p-4 ${getStatusColor(result.status)}`}>
                <div className="flex items-start gap-3">
                  {getStatusIcon(result.status)}
                  <div className="flex-1">
                    <div className="font-medium">{result.step}</div>
                    <div className="text-sm text-gray-700 mt-1">
                      {result.message}
                      {result.details && (
                        <div className="text-xs text-gray-600 mt-1">
                          {result.details}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">Como resolver problemas:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>API Key não encontrada:</strong> Configure no arquivo .env.local</li>
            <li>• <strong>Erro de carregamento:</strong> Verifique se a API "Maps JavaScript API" está ativa</li>
            <li>• <strong>Erro de permissão:</strong> Verifique restrições da API key</li>
            <li>• <strong>Cota excedida:</strong> Verifique limites no Google Cloud Console</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}