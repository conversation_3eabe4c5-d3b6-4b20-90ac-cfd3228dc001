import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { Montser<PERSON> } from "next/font/google";
import { Toaster } from "@/components/ui/toaster";

export const metadata: Metadata = {
  title: "Protetor Web",
  description: "Protetor Web - Painel de Controle",
};

const font = Montserrat({
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR" className="h-full">
      <body className={`${font.className} h-full`}>
        <div className="h-full">
          {children}
        </div>
        <Toaster />
      </body>
    </html>
  );
}
