import { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ButtonLink from "@/components/app-button-link";
import DevicesPageDetail from "./components/device-page-detail";
import { PlusIcon } from "lucide-react";
import { PageLayout } from "@/components/page-layout";

export const metadata: Metadata = {
  title: "Devices",
  description: "Listagem de Devices",
};

export default async function DevicePage() {
  return (
    <PageLayout
      title="Devices"
      description="Listagem de Devices"
      actions={
        <ButtonLink url="devices/new">
          <Button variant="outline" className="h-8 px-2 lg:px-3" size="sm">
            <PlusIcon className="mr-2 h-4 w-4" />
            Novo
          </Button>
        </ButtonLink>
      }
    >
      <DevicesPageDetail />
    </PageLayout>
  );
}