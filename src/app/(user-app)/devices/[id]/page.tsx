import { getDeviceById } from "@/service/device";
import { DeviceForm } from "../components/device-form";
import { showToast } from "@/lib/utils";
import { PageLayout } from "@/components/page-layout";
import { Device } from "@/data/model/device";

export default async function DevicesEditPage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = params;
  let device: Device | undefined;

  if (id !== "new") {
    try {
      device = await getDeviceById(id);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Erro desconhecido";

      console.error("Erro ao carregar Device:", errorMessage);
    }
  }

  return (
    <PageLayout
      title={
        id === "new"
          ? "Novo Device"
          : `Alterar Device: ${device ? device.deviceName : ""}`
      }
    >
      <DeviceForm initialData={device || null} />
    </PageLayout>
  );
}
