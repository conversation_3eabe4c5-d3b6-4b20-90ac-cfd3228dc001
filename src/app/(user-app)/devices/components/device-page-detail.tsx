"use client";

import { useEffect, useState, useCallback } from "react";
import { columns } from "./columns";
import { Skeleton } from "@/components/ui/skeleton";
import { Device } from "@/data/model/device";
import { getDeviceList } from "@/service/device";
import { DataTable } from "@/components/ui/data-table";
import { showToast } from "@/lib/utils";
import { useSearchParams, useRouter } from "next/navigation";

export default function DevicesPageDetail() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const page = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("pageSize") || "10");
  const search = searchParams.get("search") || "";

  const [registros, setRegistros] = useState<Device[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [totalCount, setTotalCount] = useState<number>(0);

  const getDevicesList = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await getDeviceList(page, pageSize, search);
      if (result) {
        setRegistros(result.data);
        setTotalCount(result.count);
      } else {
        setRegistros([]);
        setTotalCount(0);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Erro desconhecido";

      showToast(
        `Erro ao carregar Devices: ${errorMessage}`,
        "Erro: Device",
        "destructive"
      );
    } finally {
      setIsLoading(false);
    }
  }, [page, pageSize, search]);

  const onRefresh = () => {
    getDevicesList();
  };

  const onPaginationChange = (newPage: number, newPageSize: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", newPage.toString());
    params.set("pageSize", newPageSize.toString());
    router.push(`?${params.toString()}`);
  };

  const onSearchChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value && value !== "") {
      if (value !== search) {
        params.set("page", "1");
      }
    }
    params.set("search", value);
    router.push(`?${params.toString()}`);
  };

  useEffect(() => {
    getDevicesList();
  }, [getDevicesList]);

  return (
    <>
      {isLoading ? (
        <Skeleton className="h-2/3 rounded-sm" />
      ) : (
        <DataTable
          showToolbar={true}
          data={registros}
          columns={columns({ onRefresh })}
          page={page}
          pageSize={pageSize}
          totalCount={totalCount}
          onPaginationChange={onPaginationChange}
          onSearchChange={onSearchChange}
          search={search}
          onRefresh={onRefresh}
        />
      )}
    </>
  );
}
