'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { showToast } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { insertDevice, updateDevice } from '@/service/device';
import { useRouter } from 'next/navigation';
import AppFormErrors from '@/components/app-formerrors';

const deviceFormSchema = z.object({
  id: z.string().optional(),
  userId: z.string().optional(),
  fcmToken: z.string().optional(),
  type: z
    .string({
      required_error: 'Informe o tipo do device.',
    })
    .min(1, {
      message: 'Tipo do device é obrigatório.',
    }),
  deviceIdentifier: z.string().optional(),
  deviceName: z
    .string({
      required_error: 'Informe o nome do device.',
    })
    .min(2, {
      message: 'Nome do device precisa ter no mínimo 2 caracteres.',
    }),
  deviceModel: z.string().optional(),
  deviceBrand: z.string().optional(),
  osVersion: z.string().optional(),
  lastSeenAt: z.string().optional(),
  isActive: z.boolean().optional(),
  createdAt: z.string().optional(),
});

type DeviceFormValues = z.infer<typeof deviceFormSchema>;

export function DeviceForm({ initialData }: { initialData: any }) {
  const router = useRouter();
  const form = useForm<DeviceFormValues>({
    resolver: zodResolver(deviceFormSchema),
    defaultValues: initialData ? initialData : { 
      deviceName: '', 
      type: '', 
      isActive: true 
    },
    mode: 'onChange',
  });

  async function onSubmit(data: DeviceFormValues) {
    try {
      if (initialData) {
        const result = await updateDevice(data);
        showToast(
          `Device ${result?.deviceName} foi salvo com sucesso!`,
          'Device'
        );
        router.push('/devices');
      } else {
        const result = await insertDevice(data);
        showToast(
          `Device ${result?.deviceName} foi salvo com sucesso!`,
          'Device'
        );
        router.push('/devices');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';

      showToast(
        `Erro ao salvar Device: ${errorMessage}`,
        'Erro: Device',
        'destructive'
      );
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
          <AppFormErrors errors={form.formState.errors} />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name='deviceName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Device</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Informe o Nome do Device'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='type'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Ex: Android, iOS'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='deviceBrand'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Marca</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Ex: Samsung, Apple'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='deviceModel'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Modelo</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Ex: Galaxy S21, iPhone 13'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='osVersion'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Versão do SO</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Ex: 11.0, 15.2'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='userId'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ID do Usuário</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='ID do usuário associado'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name='deviceIdentifier'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Identificador do Device</FormLabel>
                <FormControl>
                  <Input
                    placeholder='Identificador único do device'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='fcmToken'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Token FCM</FormLabel>
                <FormControl>
                  <Input
                    placeholder='Token FCM para notificações push'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='isActive'
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    Device Ativo
                  </FormLabel>
                </div>
              </FormItem>
            )}
          />

          <Button type='submit' variant='outline'>
            Salvar
          </Button>
        </form>
      </Form>
    </>
  );
}