"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { showToast } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { insertDevice, updateDevice } from "@/service/device";
import { getEligibleUsersForDevices } from "@/service/user";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import AppFormErrors from "@/components/app-formerrors";
import { User } from "@/data/model/user";
import { Device } from "@/data/model/device";

// Função para mapear dados da API para o formato do formulário
function mapDeviceToFormValues(
  device: Device | null
): Partial<DeviceFormValues> {
  if (!device) {
    const defaultValues = {
      deviceName: "",
      type: "",
      deviceType: "",
      targetUserId: "",
      isActive: true,
      strapOpen: false,
    };
    return defaultValues;
  }

  const mappedValues = {
    id: device.id || "",
    targetUserId: device.targetUserId || "",
    fcmToken: device.fcmToken || "",
    deviceIdentifier: device.deviceIdentifier || "",
    deviceName: device.deviceName || "",
    deviceModel: device.deviceModel || "",
    deviceBrand: device.deviceBrand || "",
    type: device.type || "",
    deviceType: device.deviceType || "",
    osVersion: device.osVersion || "",
    strapOpen: device.strapOpen || false,
    isActive: device.isActive !== undefined ? device.isActive : true,
    lastSeenAt: device.lastSeenAt || "",
    createdAt: device.createdAt || "",
    updatedAt: device.updatedAt || "",
  };

  return mappedValues;
}

const deviceFormSchema = z.object({
  id: z.string().optional(),
  targetUserId: z
    .string({
      required_error: "Selecione um usuário.",
    })
    .min(1, {
      message: "Usuário é obrigatório.",
    }),
  fcmToken: z.string().optional(),
  deviceIdentifier: z.string().optional(),
  deviceName: z
    .string({
      required_error: "Informe o nome do device.",
    })
    .min(2, {
      message: "Nome do device precisa ter no mínimo 2 caracteres.",
    }),
  deviceModel: z.string().optional(),
  deviceBrand: z.string().optional(),
  type: z
    .string({
      required_error: "Selecione o sistema operacional.",
    })
    .min(1, {
      message: "Sistema operacional é obrigatório.",
    }),
  deviceType: z
    .string({
      required_error: "Selecione o tipo do dispositivo.",
    })
    .min(1, {
      message: "Tipo do dispositivo é obrigatório.",
    }),
  osVersion: z.string().optional(),
  strapOpen: z.boolean().optional(),
  isActive: z.boolean().optional(),
  lastSeenAt: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

type DeviceFormValues = z.infer<typeof deviceFormSchema>;

export function DeviceForm({ initialData }: { initialData: Device | null }) {
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);

  const form = useForm<DeviceFormValues>({
    resolver: zodResolver(deviceFormSchema),
    defaultValues: mapDeviceToFormValues(null), // Sempre começar vazio
    mode: "onChange",
  });

  const watchedDeviceType = form.watch("deviceType");

  useEffect(() => {
    async function loadUsers() {
      try {
        setIsLoadingUsers(true);
        const eligibleUsers = await getEligibleUsersForDevices();
        setUsers(eligibleUsers);
      } catch (error) {
        console.error("Erro ao carregar usuários:", error);
        showToast("Erro ao carregar lista de usuários", "Erro", "destructive");
      } finally {
        setIsLoadingUsers(false);
      }
    }

    loadUsers();
  }, []);

  // Carregar dados do device quando initialData estiver disponível
  useEffect(() => {
    if (initialData) {
      const mappedData = mapDeviceToFormValues(initialData);
      form.reset(mappedData);
    } else {
      // Se não há dados iniciais, usar valores padrão
      form.reset(mapDeviceToFormValues(null));
    }
  }, [initialData, form]);

  // Limpar o valor de strapOpen quando o tipo não for "Relógio"
  useEffect(() => {
    if (watchedDeviceType !== "Relógio") {
      form.setValue("strapOpen", false);
    }
  }, [watchedDeviceType, form]);

  async function onSubmit(data: DeviceFormValues) {
    try {
      if (initialData) {
        const result = await updateDevice(data);
        showToast(
          `Device ${result?.deviceName} foi salvo com sucesso!`,
          "Device"
        );
        router.push("/devices");
      } else {
        const result = await insertDevice(data);
        showToast(
          `Device ${result?.deviceName} foi salvo com sucesso!`,
          "Device"
        );
        router.push("/devices");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Erro desconhecido";

      showToast(
        `Erro ao salvar Device: ${errorMessage}`,
        "Erro: Device",
        "destructive"
      );
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <AppFormErrors errors={form.formState.errors} />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="targetUserId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Usuário *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            isLoadingUsers
                              ? "Carregando usuários..."
                              : "Selecione um usuário"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {users.map((user) => (
                        <SelectItem key={user.id} value={user.id || ""}>
                          {user.name || user.email}{" "}
                          {user.role && `(${user.role})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="deviceName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Device *</FormLabel>
                  <FormControl>
                    <Input placeholder="Informe o Nome do Device" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="deviceType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo do Dispositivo *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Celular">Celular</SelectItem>
                      <SelectItem value="Relógio">Relógio</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sistema Operacional *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o SO" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Android">Android</SelectItem>
                      <SelectItem value="iOS">iOS</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="deviceBrand"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Marca</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Samsung, Apple" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="deviceModel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Modelo</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Galaxy S21, iPhone 13" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="osVersion"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Versão do SO</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: 11.0, 15.2" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {watchedDeviceType === "Relógio" && (
              <FormField
                control={form.control}
                name="strapOpen"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Pulseira Aberta</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Marque se a pulseira estiver aberta/desconectada
                      </p>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          <FormField
            control={form.control}
            name="deviceIdentifier"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Identificador do Device</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Identificador único do device"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="fcmToken"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Token FCM</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Token FCM para notificações push"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="isActive"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Device Ativo</FormLabel>
                </div>
              </FormItem>
            )}
          />

          <Button type="submit" variant="outline">
            Salvar
          </Button>
        </form>
      </Form>
    </>
  );
}
