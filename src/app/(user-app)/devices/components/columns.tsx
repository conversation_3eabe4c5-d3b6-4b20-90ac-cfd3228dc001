'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { showToast } from '@/lib/utils';
import { Device } from '@/data/model/device';
import { deleteDevice } from '@/service/device';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { ArrowUpDown, MoreHorizontal } from 'lucide-react';
import { useRouter } from 'next/navigation';

function ActionMenu({
  id,
  onRefresh,
}: {
  id: string;
  onRefresh: (value: any) => void;
}) {
  const router = useRouter();

  const handleViewDetails = () => {
    router.push(`devices/${id}`);
  };

  const handleDelete = async (res: any) => {
    try {
      const result = await deleteDevice(id);
      showToast(
        `Device foi excluído com sucesso!`,
        'Device'
      );
      onRefresh(result);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';

      if (errorMessage.includes('Server Components render')) {
        showToast(
          'Não é possível excluir este device. Verifique se não existem registros vinculados.',
          'Erro: Device',
          'destructive'
        );
      } else {
        showToast(
          `Erro ao excluir Device: ${errorMessage}`,
          'Erro: Device',
          'destructive'
        );
      }
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' className='h-8 w-8 p-0'>
          <span className='sr-only'>Abrir Menu</span>
          <MoreHorizontal className='h-4 w-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuLabel>Ações</DropdownMenuLabel>
        <DropdownMenuItem onClick={handleViewDetails}>
          Visualizar/Alterar Registro
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleDelete}>
          Excluir Registro
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const columns = ({
  onRefresh,
}: {
  onRefresh: (reg: any) => void;
}): ColumnDef<Device>[] => [
  {
    id: 'actions',
    cell: ({ row }: { row: any }) => {
      const device = row.original;

      return <ActionMenu id={device.id} onRefresh={onRefresh} />;
    },
  },
  {
    accessorKey: 'deviceName',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Nome do Device
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
  },
  {
    accessorKey: 'type',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Tipo
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
  },
  {
    accessorKey: 'deviceBrand',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Marca
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
  },
  {
    accessorKey: 'deviceModel',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Modelo
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
  },
  {
    accessorKey: 'osVersion',
    header: 'Versão OS',
  },
  {
    accessorKey: 'isActive',
    header: 'Ativo',
    cell: ({ getValue }: { getValue: any }) => {
      const isActive = getValue() as boolean;
      return isActive ? 'Sim' : 'Não';
    },
  },
  {
    accessorKey: 'lastSeenAt',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Última Visualização
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
    cell: ({ getValue }: { getValue: any }) => {
      const dateValue = getValue() as string;
      if (!dateValue) return '-';
      return format(new Date(dateValue), 'dd/MM/yyyy HH:mm');
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Data de Criação
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
    cell: ({ getValue }: { getValue: any }) => {
      const dateValue = getValue() as string;
      if (!dateValue) return '-';
      return format(new Date(dateValue), 'dd/MM/yyyy HH:mm');
    },
  },
];