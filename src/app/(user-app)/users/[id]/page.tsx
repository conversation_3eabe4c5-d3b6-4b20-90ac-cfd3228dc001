'use client';

import { getUserById } from '@/service/user';
import { UserForm } from '../components/user-form';
import { showToast } from '@/lib/utils';
import { useEffect, useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { PageLayout } from '@/components/page-layout';

export default function UsersEditPage({ params }: { params: any }) {
  const { id } = params;
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (id !== 'new') {
      setLoading(true);
      getUserById(id)
        .then((userData) => {
          setUser(userData);
        })
        .catch((error) => {
          const errorMessage =
            error instanceof Error ? error.message : 'Erro desconhecido';

          console.error('Erro ao carregar Usuário:', errorMessage);
          showToast(
            `Erro ao carregar usuário: ${errorMessage}`,
            'Erro: Usuário',
            'destructive'
          );
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [id]);

  if (loading) {
    return (
      <PageLayout title="Carregando usuário...">
        <Skeleton className="h-96 w-full" />
      </PageLayout>
    );
  }

  return (
    <PageLayout 
      title={id === 'new' ? 'Novo Usuário' : `Alterar Usuário: ${user ? user.name || user.email : ''}`}
    >
      <UserForm initialData={user} />
    </PageLayout>
  );
}