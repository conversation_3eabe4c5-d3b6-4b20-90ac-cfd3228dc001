import { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ButtonLink from "@/components/app-button-link";
import UsersPageDetail from "./components/user-page-detail";
import { PlusIcon } from "lucide-react";
import { PageLayout } from "@/components/page-layout";

export const metadata: Metadata = {
  title: "Usuários",
  description: "Listagem de Usuários",
};

export default async function UsersPage() {
  return (
    <PageLayout
      title="Usuários"
      description="Listagem de Usuários"
      actions={
        <ButtonLink url="users/new">
          <Button variant="outline" className="h-8 px-2 lg:px-3" size="sm">
            <PlusIcon className="mr-2 h-4 w-4" />
            Novo
          </Button>
        </ButtonLink>
      }
    >
      <UsersPageDetail />
    </PageLayout>
  );
}