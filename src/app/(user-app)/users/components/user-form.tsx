'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { showToast } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { insertUser, updateUser } from '@/service/user';
import { useRouter } from 'next/navigation';
import AppFormErrors from '@/components/app-formerrors';

const userFormSchema = z.object({
  id: z.string().optional(),
  name: z
    .string({
      required_error: 'Informe o nome do usuário.',
    })
    .min(2, {
      message: 'Nome precisa ter no mínimo 2 caracteres.',
    }),
  email: z
    .string({
      required_error: 'Informe o email do usuário.',
    })
    .email({
      message: 'Digite um email válido.',
    }),
  password: z
    .string()
    .optional()
    .refine((val) => {
      // Se não há initialData (novo usuário), password é obrigatório
      return val && val.length >= 6;
    }, {
      message: 'Senha precisa ter no mínimo 6 caracteres.',
    }),
  role: z
    .string({
      required_error: 'Selecione um papel para o usuário.',
    })
    .min(1, {
      message: 'Papel é obrigatório.',
    }),
});

type UserFormValues = z.infer<typeof userFormSchema>;

export function UserForm({ initialData }: { initialData: any }) {
  const router = useRouter();
  const isEditing = !!initialData;
  
  // Para edição, password não é obrigatório
  const editSchema = isEditing 
    ? userFormSchema.extend({
        password: z.string().optional(),
      }) 
    : userFormSchema;

  const form = useForm<UserFormValues>({
    resolver: zodResolver(editSchema),
    defaultValues: initialData ? {
      id: initialData.id,
      name: initialData.name || '',
      email: initialData.email || '',
      password: '',
      role: initialData.role || '',
    } : { 
      name: '', 
      email: '',
      password: '',
      role: '',
    },
    mode: 'onChange',
  });

  async function onSubmit(data: UserFormValues) {
    try {
      if (initialData) {
        // Se não informou password, remove do objeto
        if (!data.password || data.password.trim() === '') {
          delete data.password;
        }
        const result = await updateUser(data);
        showToast(
          `Usuário ${result?.name || result?.email} foi salvo com sucesso!`,
          'Usuário'
        );
        router.push('/users');
      } else {
        const result = await insertUser(data);
        showToast(
          `Usuário ${result?.name || result?.email} foi salvo com sucesso!`,
          'Usuário'
        );
        router.push('/users');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';

      showToast(
        `Erro ao salvar Usuário: ${errorMessage}`,
        'Erro: Usuário',
        'destructive'
      );
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
          <AppFormErrors errors={form.formState.errors} />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Informe o nome do usuário'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='email'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type='email'
                      placeholder='<EMAIL>'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Senha
                  {isEditing && (
                    <span className="text-sm text-muted-foreground ml-2">
                      (deixe em branco para manter a senha atual)
                    </span>
                  )}
                </FormLabel>
                <FormControl>
                  <Input
                    type='password'
                    placeholder={isEditing ? 'Nova senha (opcional)' : 'Digite uma senha'}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='role'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Papel</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um papel" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="SuperAdmin">Super Administrador</SelectItem>
                    <SelectItem value="Admin">Administrador</SelectItem>
                    <SelectItem value="SecurityOfficer">Oficial de Segurança</SelectItem>
                    <SelectItem value="Victim">Vítima</SelectItem>
                    <SelectItem value="Aggressor">Agressor</SelectItem>
                    <SelectItem value="PlainUser">Usuário Comum</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type='submit' variant='outline'>
            Salvar
          </Button>
        </form>
      </Form>
    </>
  );
}