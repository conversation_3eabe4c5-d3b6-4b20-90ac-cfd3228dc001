'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { showToast } from '@/lib/utils';
import { User } from '@/data/model/user';
import { deleteUser } from '@/service/user';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { ArrowUpDown, MoreHorizontal } from 'lucide-react';
import { useRouter } from 'next/navigation';

function ActionMenu({
  id,
  onRefresh,
}: {
  id: string;
  onRefresh: (value: any) => void;
}) {
  const router = useRouter();

  const handleViewDetails = () => {
    router.push(`users/${id}`);
  };

  const handleDelete = async (res: any) => {
    try {
      const result = await deleteUser(id);
      showToast(
        `Usuário foi excluído com sucesso!`,
        'Usuário'
      );
      onRefresh(result);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';

      if (errorMessage.includes('Server Components render')) {
        showToast(
          'Não é possível excluir este usuário. Verifique se não existem registros vinculados.',
          'Erro: Usuário',
          'destructive'
        );
      } else {
        showToast(
          `Erro ao excluir Usuário: ${errorMessage}`,
          'Erro: Usuário',
          'destructive'
        );
      }
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' className='h-8 w-8 p-0'>
          <span className='sr-only'>Abrir Menu</span>
          <MoreHorizontal className='h-4 w-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuLabel>Ações</DropdownMenuLabel>
        <DropdownMenuItem onClick={handleViewDetails}>
          Visualizar/Alterar Registro
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleDelete}>
          Excluir Registro
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const columns = ({
  onRefresh,
}: {
  onRefresh: (reg: any) => void;
}): ColumnDef<User>[] => [
  {
    id: 'actions',
    cell: ({ row }: { row: any }) => {
      const user = row.original;

      return <ActionMenu id={user.id} onRefresh={onRefresh} />;
    },
  },
  {
    accessorKey: 'name',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Nome
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
  },
  {
    accessorKey: 'email',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Email
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
  },
  {
    accessorKey: 'role',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Papel
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
    cell: ({ getValue }: { getValue: any }) => {
      const roleValue = getValue() as string;
      const roleLabels: Record<string, string> = {
        'SuperAdmin': 'Super Administrador',
        'Admin': 'Administrador',
        'SecurityOfficer': 'Oficial de Segurança',
        'Victim': 'Vítima',
        'Aggressor': 'Agressor',
        'PlainUser': 'Usuário Comum'
      };
      return roleLabels[roleValue] || roleValue || '-';
    },
  },
  {
    accessorKey: 'created_at',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Data de Criação
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
    cell: ({ getValue }: { getValue: any }) => {
      const dateValue = getValue() as string;
      if (!dateValue) return '-';
      return format(new Date(dateValue), 'dd/MM/yyyy HH:mm');
    },
  },
  {
    accessorKey: 'updated_at',
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Última Atualização
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
    cell: ({ getValue }: { getValue: any }) => {
      const dateValue = getValue() as string;
      if (!dateValue) return '-';
      return format(new Date(dateValue), 'dd/MM/yyyy HH:mm');
    },
  },
];