"use client";

import { useEffect, useState, useCallback } from "react";
import { columns } from "./columns";
import { Skeleton } from "@/components/ui/skeleton";
import { User } from "@/data/model/user";
import { getUserList } from "@/service/user";
import { DataTable } from "@/components/ui/data-table";
import { showToast } from "@/lib/utils";
import { useSearchParams, useRouter } from "next/navigation";

export default function UsersPageDetail() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const page = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("pageSize") || "10");
  const search = searchParams.get("search") || "";

  const [registros, setRegistros] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [totalCount, setTotalCount] = useState<number>(0);

  const getUsersList = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await getUserList(page, pageSize, search);
      if (result) {
        setRegistros(result.data);
        setTotalCount(result.count);
      } else {
        setRegistros([]);
        setTotalCount(0);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Erro desconhecido";

      showToast(
        `Erro ao carregar Usuários: ${errorMessage}`,
        "Erro: Usuário",
        "destructive"
      );
    } finally {
      setIsLoading(false);
    }
  }, [page, pageSize, search]);

  const onRefresh = () => {
    getUsersList();
  };

  const onPaginationChange = (newPage: number, newPageSize: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", newPage.toString());
    params.set("pageSize", newPageSize.toString());
    router.push(`?${params.toString()}`);
  };

  const onSearchChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value && value !== "") {
      if (value !== search) {
        params.set("page", "1");
      }
    }
    params.set("search", value);
    router.push(`?${params.toString()}`);
  };

  useEffect(() => {
    getUsersList();
  }, [getUsersList]);

  return (
    <>
      {isLoading ? (
        <Skeleton className="h-2/3 rounded-sm" />
      ) : (
        <DataTable
          showToolbar={true}
          data={registros}
          columns={columns({ onRefresh })}
          page={page}
          pageSize={pageSize}
          totalCount={totalCount}
          onPaginationChange={onPaginationChange}
          onSearchChange={onSearchChange}
          search={search}
          onRefresh={onRefresh}
        />
      )}
    </>
  );
}