import { AppSidebar } from '@/components/app-sidebar';
import { Separator } from '@/components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';
import AppBreadCrumb from '@/components/app-breadcrumb';
import { AuthGuard } from '@/components/auth-guard';

export default function Layout({
  children,
}: {
  children: React.ReactNode;
}) {

  return (
    <AuthGuard>
      <SidebarProvider>
        <AppSidebar collapsible='icon' />
        <SidebarInset className='flex flex-col h-screen overflow-hidden'>
          <header className='flex h-16 shrink-0 items-center gap-2 border-b px-4'>
            <SidebarTrigger className='-ml-1' />
            <Separator orientation='vertical' className='mr-2 h-4' />
            <AppBreadCrumb />
          </header>
          <div className='flex-1 min-h-0'>{children}</div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  );
}
