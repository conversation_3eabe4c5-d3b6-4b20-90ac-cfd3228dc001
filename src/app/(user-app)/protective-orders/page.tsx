import { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ButtonLink from "@/components/app-button-link";
import ProtectiveOrdersPageDetail from "./components/protective-orders-page-detail";
import { PlusIcon } from "lucide-react";
import { PageLayout } from "@/components/page-layout";

export const metadata: Metadata = {
  title: "Medidas Protetivas",
  description: "Listagem de Medidas Protetivas",
};

export default async function ProtectiveOrdersPage() {
  return (
    <PageLayout
      title="Medidas Protetivas"
      description="Listagem de Medidas Protetivas"
      actions={
        <ButtonLink url="protective-orders/new">
          <Button variant="outline" className="h-8 px-2 lg:px-3" size="sm">
            <PlusIcon className="mr-2 h-4 w-4" />
            Nova
          </Button>
        </ButtonLink>
      }
    >
      <ProtectiveOrdersPageDetail />
    </PageLayout>
  );
}