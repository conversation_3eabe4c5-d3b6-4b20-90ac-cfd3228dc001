'use client';

import { getProtectiveOrderById } from '@/service/protective-order';
import { ProtectiveOrderForm } from '../components/protective-order-form';
import { showToast } from '@/lib/utils';
import { useEffect, useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { PageLayout } from '@/components/page-layout';

export default function ProtectiveOrdersEditPage({ params }: { params: any }) {
  const { id } = params;
  const [protectiveOrder, setProtectiveOrder] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (id !== 'new') {
      setLoading(true);
      getProtectiveOrderById(id)
        .then((protectiveOrderData) => {
          setProtectiveOrder(protectiveOrderData);
        })
        .catch((error) => {
          const errorMessage =
            error instanceof Error ? error.message : 'Erro desconhecido';

          console.error('Erro ao carregar Medida Protetiva:', errorMessage);
          showToast(
            `Erro ao carregar medida protetiva: ${errorMessage}`,
            'Erro: Medida Protetiva',
            'destructive'
          );
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [id]);

  if (loading) {
    return (
      <PageLayout title="Carregando medida protetiva...">
        <Skeleton className="h-96 w-full" />
      </PageLayout>
    );
  }

  return (
    <PageLayout 
      title={id === 'new' ? 'Nova Medida Protetiva' : `Alterar Medida Protetiva: ${protectiveOrder ? protectiveOrder.judicialCaseNumber || 'sem número' : ''}`}
    >
      <ProtectiveOrderForm initialData={id === 'new' ? null : protectiveOrder} />
    </PageLayout>
  );
}