'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { showToast } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { insertProtectiveOrder, updateProtectiveOrder } from '@/service/protective-order';
import { useRouter } from 'next/navigation';
import AppFormErrors from '@/components/app-formerrors';
import { UserSelect } from '@/components/user-select';

const protectiveOrderFormSchema = z.object({
  id: z.string().optional(),
  victimId: z
    .string({
      required_error: 'Selecione a vítima.',
    })
    .min(1, {
      message: 'Vítima é obrigatória.',
    }),
  aggressorId: z
    .string({
      required_error: 'Selecione o agressor.',
    })
    .min(1, {
      message: 'Agressor é obrigatório.',
    }),
  minimumDistanceInMeters: z
    .number({
      required_error: 'Informe a distância mínima em metros.',
    })
    .min(1, {
      message: 'Distância mínima deve ser maior que 0.',
    }),
  isActive: z.boolean().default(true),
  judicialCaseNumber: z
    .string({
      required_error: 'Informe o número do processo judicial.',
    })
    .min(1, {
      message: 'Número do processo judicial é obrigatório.',
    }),
});

type ProtectiveOrderFormValues = z.infer<typeof protectiveOrderFormSchema>;

export function ProtectiveOrderForm({ initialData }: { initialData: any }) {
  const router = useRouter();
  const isEditing = !!initialData;

  const form = useForm<ProtectiveOrderFormValues>({
    resolver: zodResolver(protectiveOrderFormSchema),
    defaultValues: initialData ? {
      id: initialData.id,
      victimId: initialData.victimId || '',
      aggressorId: initialData.aggressorId || '',
      minimumDistanceInMeters: initialData.minimumDistanceInMeters || 100,
      isActive: initialData.isActive ?? true,
      judicialCaseNumber: initialData.judicialCaseNumber || '',
    } : { 
      victimId: '',
      aggressorId: '',
      minimumDistanceInMeters: 100,
      isActive: true,
      judicialCaseNumber: '',
    },
    mode: 'onChange',
  });

  async function onSubmit(data: ProtectiveOrderFormValues) {
    try {
      if (initialData) {
        const result = await updateProtectiveOrder(data);
        showToast(
          `Medida Protetiva ${result?.judicialCaseNumber || 'sem número'} foi salva com sucesso!`,
          'Medida Protetiva'
        );
        router.push('/protective-orders');
      } else {
        const result = await insertProtectiveOrder(data);
        showToast(
          `Medida Protetiva ${result?.judicialCaseNumber || 'sem número'} foi salva com sucesso!`,
          'Medida Protetiva'
        );
        router.push('/protective-orders');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';

      showToast(
        `Erro ao salvar Medida Protetiva: ${errorMessage}`,
        'Erro: Medida Protetiva',
        'destructive'
      );
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
          <AppFormErrors errors={form.formState.errors} />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name='judicialCaseNumber'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Número do Processo Judicial</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Ex: 2025001234'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='minimumDistanceInMeters'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Distância Mínima (metros)</FormLabel>
                  <FormControl>
                    <Input
                      type='number'
                      min='1'
                      placeholder='100'
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name='victimId'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Vítima</FormLabel>
                  <FormControl>
                    <UserSelect
                      role='Victim'
                      placeholder='Selecione a vítima...'
                      value={field.value}
                      onValueChange={(value, user) => {
                        field.onChange(value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='aggressorId'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Agressor</FormLabel>
                  <FormControl>
                    <UserSelect
                      role='Aggressor'
                      placeholder='Selecione o agressor...'
                      value={field.value}
                      onValueChange={(value, user) => {
                        field.onChange(value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name='isActive'
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 bg-muted/30">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    Status da Medida Protetiva
                  </FormLabel>
                  <div className="text-sm text-muted-foreground">
                    Campo informativo - status atual da medida protetiva
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-medium ${field.value ? 'text-green-600' : 'text-red-600'}`}>
                    {field.value ? 'Ativa' : 'Inativa'}
                  </span>
                  <Switch
                    checked={field.value}
                    disabled={true}
                  />
                </div>
              </FormItem>
            )}
          />

          <Button type='submit' variant='outline'>
            Salvar
          </Button>
        </form>
      </Form>
    </>
  );
}