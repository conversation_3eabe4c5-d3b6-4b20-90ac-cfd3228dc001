"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { showToast } from "@/lib/utils";
import { activateProtectiveOrder, deactivateProtectiveOrder } from "@/service/protective-order";

// Interface para controle dos modais
interface ModalControlProps {
  openActivateModal: (id: string, onRefresh: (value: any) => void) => void;
  openDeactivateModal: (id: string, onRefresh: (value: any) => void) => void;
}

// Referência global para as funções de controle dos modais
let modalControls: ModalControlProps | null = null;

// Função para acessar os controles dos modais externamente
export const getModalControls = () => modalControls;

// Componente para gerenciar os modais de medidas protetivas
export function ProtectiveOrderModals() {
  const [isActivateDialogOpen, setIsActivateDialogOpen] = useState(false);
  const [isDeactivateDialogOpen, setIsDeactivateDialogOpen] = useState(false);
  const [currentId, setCurrentId] = useState("");
  const [currentOnRefresh, setCurrentOnRefresh] = useState<((value: any) => void) | null>(null);
  const [activateReason, setActivateReason] = useState("");
  const [deactivateReason, setDeactivateReason] = useState("");
  const [modalKey, setModalKey] = useState(0);

  // Configurar funções globais de controle dos modais
  useEffect(() => {
    modalControls = {
      openActivateModal: (id: string, onRefresh: (value: any) => void) => {
        setCurrentId(id);
        setCurrentOnRefresh(() => onRefresh);
        setIsDeactivateDialogOpen(false);
        setDeactivateReason("");
        setIsActivateDialogOpen(true);
      },
      openDeactivateModal: (id: string, onRefresh: (value: any) => void) => {
        setCurrentId(id);
        setCurrentOnRefresh(() => onRefresh);
        setIsActivateDialogOpen(false);
        setActivateReason("");
        setIsDeactivateDialogOpen(true);
      },
    };

    return () => {
      modalControls = null;
    };
  }, []);

  const closeAllModals = () => {
    setIsActivateDialogOpen(false);
    setIsDeactivateDialogOpen(false);
    setActivateReason("");
    setDeactivateReason("");
    setCurrentId("");
    setCurrentOnRefresh(null);
    setModalKey((prev) => prev + 1);

    performSafeCleanup();
  };

  const performSafeCleanup = () => {
    const safeCleanup = () => {
      try {
        // Remove overlays específicos do Radix UI Dialog
        const radixOverlays = document.querySelectorAll("[data-radix-dialog-overlay]");
        radixOverlays.forEach((overlay) => {
          if (overlay.parentNode) {
            overlay.remove();
          }
        });

        // Remove containers do Radix UI Dialog
        const radixContainers = document.querySelectorAll("[data-radix-dialog-content]");
        radixContainers.forEach((container) => {
          if (container.parentNode) {
            container.remove();
          }
        });

        // Limpa atributos do body
        document.body.style.removeProperty("pointer-events");
        document.body.style.removeProperty("overflow");
        document.body.classList.remove("overflow-hidden");
      } catch (error) {
        // Silenciar erros da limpeza
      }
    };

    // Executar limpeza com delays
    setTimeout(safeCleanup, 10);
    setTimeout(safeCleanup, 100);
  };

  // Listener para escape key como backup
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && (isActivateDialogOpen || isDeactivateDialogOpen)) {
        closeAllModals();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isActivateDialogOpen, isDeactivateDialogOpen]);

  const handleActivateConfirm = async () => {
    if (!currentId || !currentOnRefresh) return;

    try {
      const result = await activateProtectiveOrder(
        currentId,
        activateReason.trim() || undefined
      );
      showToast("Medida Protetiva foi ativada com sucesso!", "Medida Protetiva");
      currentOnRefresh(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
      showToast(
        `Erro ao ativar Medida Protetiva: ${errorMessage}`,
        "Erro: Medida Protetiva",
        "destructive"
      );
    } finally {
      closeAllModals();
    }
  };

  const handleDeactivateConfirm = async () => {
    if (!currentId || !currentOnRefresh) return;

    if (!deactivateReason.trim()) {
      showToast(
        "Por favor, informe o motivo da desativação.",
        "Campo obrigatório",
        "destructive"
      );
      return;
    }

    try {
      const result = await deactivateProtectiveOrder(currentId, deactivateReason);
      showToast("Medida Protetiva foi desativada com sucesso!", "Medida Protetiva");
      currentOnRefresh(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
      showToast(
        `Erro ao desativar Medida Protetiva: ${errorMessage}`,
        "Erro: Medida Protetiva",
        "destructive"
      );
    } finally {
      closeAllModals();
    }
  };

  return (
    <>
      {/* Modal de Ativação */}
      <Dialog
        key={`activate-${modalKey}`}
        open={isActivateDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            closeAllModals();
          }
        }}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Ativar Medida Protetiva</DialogTitle>
            <DialogDescription>
              Você pode informar um motivo para a ativação desta medida protetiva. Este campo é
              opcional.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="activate-reason">Motivo da ativação (opcional)</Label>
              <Textarea
                id="activate-reason"
                placeholder="Informe o motivo para ativar a medida protetiva (opcional)..."
                value={activateReason}
                onChange={(e) => setActivateReason(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={closeAllModals}>
              Cancelar
            </Button>
            <Button onClick={handleActivateConfirm} className="bg-green-600 hover:bg-green-700">
              Ativar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Desativação */}
      <Dialog
        key={`deactivate-${modalKey}`}
        open={isDeactivateDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            closeAllModals();
          }
        }}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Desativar Medida Protetiva</DialogTitle>
            <DialogDescription>
              Para desativar esta medida protetiva, é necessário informar o motivo. Este campo é
              obrigatório.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="reason">Motivo da desativação *</Label>
              <Textarea
                id="reason"
                placeholder="Informe o motivo para desativar a medida protetiva..."
                value={deactivateReason}
                onChange={(e) => setDeactivateReason(e.target.value)}
                maxLength={500}
                className="min-h-[100px]"
              />
              <p className="text-sm text-muted-foreground">
                {deactivateReason.length}/500 caracteres
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={closeAllModals}>
              Cancelar
            </Button>
            <Button
              onClick={handleDeactivateConfirm}
              variant="destructive"
              disabled={!deactivateReason.trim()}
            >
              Desativar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}