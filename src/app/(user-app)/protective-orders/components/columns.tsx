"use client";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { showToast } from "@/lib/utils";
import { ProtectiveOrder } from "@/data/model/protective-order";
import { deleteProtectiveOrder } from "@/service/protective-order";
import { ColumnDef, Column, Row } from "@tanstack/react-table";
import { format } from "date-fns";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import { useRouter } from "next/navigation";
import { getModalControls } from "./protective-order-modals";

interface ActionMenuProps {
  id: string;
  isActive: boolean;
  onRefresh: (value: any) => void;
}

function ActionMenu({ id, isActive, onRefresh }: ActionMenuProps) {
  const router = useRouter();

  const handleViewDetails = () => {
    router.push(`protective-orders/${id}`);
  };

  const handleActivateClick = () => {
    const modalControls = getModalControls();
    if (modalControls) {
      modalControls.openActivateModal(id, onRefresh);
    }
  };

  const handleDeactivateClick = () => {
    const modalControls = getModalControls();
    if (modalControls) {
      modalControls.openDeactivateModal(id, onRefresh);
    }
  };

  const handleDelete = async () => {
    try {
      const result = await deleteProtectiveOrder(id);
      showToast(
        "Medida Protetiva foi excluída com sucesso!",
        "Medida Protetiva"
      );
      onRefresh(result);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Erro desconhecido";

      if (errorMessage.includes("Server Components render")) {
        showToast(
          "Não é possível excluir esta medida protetiva. Verifique se não existem registros vinculados.",
          "Erro: Medida Protetiva",
          "destructive"
        );
      } else {
        showToast(
          `Erro ao excluir Medida Protetiva: ${errorMessage}`,
          "Erro: Medida Protetiva",
          "destructive"
        );
      }
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Abrir Menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Ações</DropdownMenuLabel>
        <DropdownMenuItem onClick={handleViewDetails}>
          Visualizar/Alterar Registro
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        {!isActive ? (
          <DropdownMenuItem onClick={handleActivateClick}>
            Ativar Medida Protetiva
          </DropdownMenuItem>
        ) : (
          <DropdownMenuItem onClick={handleDeactivateClick}>
            Desativar Medida Protetiva
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleDelete}>
          Excluir Registro
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface ColumnsProps {
  onRefresh: (reg: any) => void;
}

export const columns = ({
  onRefresh,
}: ColumnsProps): ColumnDef<ProtectiveOrder>[] => [
  {
    id: "actions",
    cell: ({ row }: { row: Row<ProtectiveOrder> }) => {
      const protectiveOrder = row.original;
      return (
        <ActionMenu
          id={protectiveOrder.id || ""}
          isActive={protectiveOrder.isActive ?? false}
          onRefresh={onRefresh}
        />
      );
    },
  },
  {
    accessorKey: "judicialCaseNumber",
    header: ({ column }: { column: Column<ProtectiveOrder> }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Processo Judicial
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "victimName",
    header: ({ column }: { column: Column<ProtectiveOrder> }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Vítima
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "aggressorName",
    header: ({ column }: { column: Column<ProtectiveOrder> }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Agressor
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "minimumDistanceInMeters",
    header: ({ column }: { column: Column<ProtectiveOrder> }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Distância Mínima (m)
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ getValue }: { getValue: () => any }) => {
      const distance = getValue() as number;
      return distance ? `${distance}m` : "-";
    },
  },
  {
    accessorKey: "isActive",
    header: ({ column }: { column: Column<ProtectiveOrder> }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ getValue }: { getValue: () => any }) => {
      const isActive = getValue() as boolean;
      return (
        <span
          className={`px-2 py-1 rounded text-sm ${
            isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
          }`}
        >
          {isActive ? "Ativa" : "Inativa"}
        </span>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }: { column: Column<ProtectiveOrder> }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Data de Criação
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ getValue }: { getValue: () => any }) => {
      const dateValue = getValue() as string;
      if (!dateValue) return "-";
      return format(new Date(dateValue), "dd/MM/yyyy HH:mm");
    },
  },
  {
    accessorKey: "updatedAt",
    header: ({ column }: { column: Column<ProtectiveOrder> }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Última Atualização
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ getValue }: { getValue: () => any }) => {
      const dateValue = getValue() as string;
      if (!dateValue || dateValue === "0001-01-01T00:00:00") return "-";
      return format(new Date(dateValue), "dd/MM/yyyy HH:mm");
    },
  },
];
