import { NextRequest, NextResponse } from "next/server";
import { getMockLocationData } from "@/data/mock/location-tracking-mock";
import { getLastLocation } from "@/service/location";
import { LocationResponse } from "@/data/model/location";

export async function GET(
  request: NextRequest,
  { params }: { params: { uuid: string } }
) {
  try {
    const { uuid } = params;
    
    if (!uuid) {
      return NextResponse.json(
        {
          success: false,
          message: "UUID é obrigatório",
          data: null,
          errors: ["UUID não fornecido"],
          timestamp: new Date().toISOString(),
        } as LocationResponse,
        { status: 400 }
      );
    }

    // Verificar se deve usar dados mock baseado na variável de ambiente
    const useMockData = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true';
    
    let locationData: LocationResponse;
    
    if (useMockData) {
      console.info('Usando dados mock de localização (NEXT_PUBLIC_USE_MOCK_DATA=true)');
      locationData = await getMockLocationData(uuid);
    } else {
      try {
        // Tentar usar API real através do service
        console.info('Tentando usar API real de localização (NEXT_PUBLIC_USE_MOCK_DATA=false)');
        const serviceResponse = await getLastLocation(uuid);
        
        if (serviceResponse) {
          locationData = serviceResponse;
        } else {
          throw new Error('Service retornou undefined');
        }
      } catch (apiError) {
        console.warn('API real falhou, usando dados mock como fallback:', apiError);
        // Se a API real falhar, usar dados mock como fallback
        locationData = await getMockLocationData(uuid);
      }
    }

    return NextResponse.json(locationData, {
      status: locationData.success ? 200 : (locationData.data === null ? 404 : 500)
    });

  } catch (error) {
    console.error("Erro na API de localização:", error);
    
    return NextResponse.json(
      {
        success: false,
        message: "Erro interno do servidor",
        data: null,
        errors: ["Erro inesperado na API"],
        timestamp: new Date().toISOString(),
      } as LocationResponse,
      { status: 500 }
    );
  }
}