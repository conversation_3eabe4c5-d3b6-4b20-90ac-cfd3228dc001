import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const searchParams = req.nextUrl.searchParams;
  const audioUrl = searchParams.get("url");

  if (!audioUrl) {
    return NextResponse.json({ error: "URL inválida" }, { status: 400 });
  }

  const response = await fetch(audioUrl);

  if (!response.ok) {
    return NextResponse.json({ error: "Erro ao obter áudio" }, { status: 500 });
  }

  const blob = await response.blob();

  return new NextResponse(blob, {
    headers: {
      "Content-Type": "audio/wav",
      "Content-Disposition": 'inline; filename="audio.wav"',
    },
  });
}
