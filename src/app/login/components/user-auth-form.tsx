'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import { Icons } from '@/components/ui/icons';
import { showToast } from '@/lib/utils';
import { useAuth } from '@/hooks/use-auth';

const accountFormSchema = z.object({
  email: z
    .string({
      required_error: 'Email precisa ser informado.',
    })
    .min(10, { message: 'Email precisa ter no mínimo 10 caracteres.' }),
  password: z
    .string({
      required_error: 'Senha precisa ser informada.',
    })
    .min(6, { message: 'Senha precisa ter no mínimo 6 caracteres.' }),
});

type AccountFormValues = z.infer<typeof accountFormSchema>;

const defaultValues: Partial<AccountFormValues> = {
  email: '',
  password: '',
};

export function UserAuthForm() {
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const router = useRouter();
  const { login } = useAuth();

  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues,
  });

  async function onSubmit(data: AccountFormValues) {
    setIsLoading(true);
    
    try {
      const response = await login(data.email, data.password);
      
      if (response.success) {
        router.push('/');
      } else {
        showToast(response.message || 'Erro ao fazer Login', 'Erro: Login', 'destructive');
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || error?.message || 'Erro ao conectar com o servidor';
      showToast(errorMessage, 'Erro: Login', 'destructive');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormLabel>E-mail</FormLabel>
              <FormControl>
                <Input
                  placeholder='<EMAIL>'
                  type='email'
                  disabled={isLoading}
                  {...field}
                />
              </FormControl>
              {/* <FormDescription>Descrição do campo Email</FormDescription> */}
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='password'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Senha</FormLabel>
              <FormControl>
                <Input
                  placeholder='******'
                  type='password'
                  disabled={isLoading}
                  {...field}
                />
              </FormControl>
              {/* <FormDescription>Descrição do campo Password</FormDescription> */}
              <FormMessage />
            </FormItem>
          )}
        />

        <div className='flex flex-col'>
          <Button disabled={isLoading} type='submit'>
            {isLoading && (
              <Icons.spinner className='mr-2 h-4 w-4 animate-spin' />
            )}
            Entrar
          </Button>
        </div>
      </form>
    </Form>
  );
}
