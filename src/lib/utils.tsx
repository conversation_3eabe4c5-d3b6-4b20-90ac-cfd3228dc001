import { AppProgressProps } from "@/components/app-progress";
import { toast } from "@/hooks/use-toast";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function showToast(
  description: string,
  title: string,
  variant: "default" | "destructive" = "default"
) {
  toast({
    duration: 3000,
    variant: variant,
    title: title,
    description: (
      <>
        <p>{description}</p>
      </>
    ),
  });
}

export function getScorePerc(
  score_max: number | undefined,
  score: number | undefined
): number {
  if (score && score_max && score > 0 && score_max > 0) {
    return (score / score_max) * 100;
  } else {
    return 0;
  }
}

export function getVariantProgress(score: number): AppProgressProps["variant"] {
  if (score >= 80) {
    return "success";
  } else if (score > 50 && score < 80) {
    return "default";
  } else {
    return "danger";
  }
}

export function verifyAudioWav(audioUrl: string): boolean {
  // Verifica se termina com .wav, ou contém .wav na query string ou e url do padrao bitphone que retorna wav
  const isWav =
    audioUrl.toLowerCase().endsWith(".wav") ||
    audioUrl.toLowerCase().includes(".wav") ||
    audioUrl.toLowerCase().includes("download_audio.php");

  return isWav;
}

export function formatTime(milliseconds: number) {
  // Função para formatar o tempo (milissegundos para [MM:SS])
  if (milliseconds === -1) return ""; // Não exibe tempo se for -1

  const totalSeconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const remainingSeconds = Math.floor(totalSeconds % 60);
  const formattedMinutes = String(minutes).padStart(2, "0");
  const formattedSeconds = String(remainingSeconds).padStart(2, "0");
  return `[${formattedMinutes}:${formattedSeconds}]`;
}

export function formatDecimal(value: number): string {
  return value.toFixed(2).replace(".", ",");
}

export function formatDateTime(timestamp: string): string {
  return new Date(timestamp).toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}
