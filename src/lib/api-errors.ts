export class ApiError extends Error {
  constructor(message: string, public readonly code?: string) {
    super(message);
    this.name = 'ApiError';
  }
}

export function handleApiError(error: any): never {
  // Mensagens amigáveis para usuários
  const userFriendlyMessages: Record<string, string> = {
    '23505': 'Este registro já existe no sistema.',
    '23503': 'Não é possível excluir pois existem registros vinculados.',
    default:
      'Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente.',
  };

  // Log do erro original em desenvolvimento
  if (process.env.NODE_ENV === 'development') {
    console.error('Erro API:', error);
  }

  // Determina a mensagem apropriada
  let message = userFriendlyMessages.default;
  if (error?.code && userFriendlyMessages[error.code]) {
    message = userFriendlyMessages[error.code];
  }

  throw new ApiError(message, error?.code);
}
