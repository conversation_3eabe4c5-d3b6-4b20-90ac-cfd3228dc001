"use client";

import { ActiveViolationsSummaryResponse } from "@/data/model/violation";
import { apiClient } from "@/lib/api-client";

export async function getActiveViolations(): Promise<ActiveViolationsSummaryResponse | undefined> {
  try {
    const response = await apiClient.get<ActiveViolationsSummaryResponse>('/monitoring/active-violations');

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}