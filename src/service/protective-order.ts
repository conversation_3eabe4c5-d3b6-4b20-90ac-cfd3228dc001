"use client";

import { ProtectiveOrder, ProtectiveOrderDataParam } from "@/data/model/protective-order";
import { apiClient } from "@/lib/api-client";

export async function getProtectiveOrderList(
  page?: number,
  pageSize?: number,
  search?: string
): Promise<{ data: ProtectiveOrder[]; count: number } | undefined> {
  try {
    const params = new URLSearchParams();
    if (page) params.append('page', page.toString());
    if (pageSize) params.append('pageSize', pageSize.toString());
    if (search) params.append('search', search);

    const queryString = params.toString();
    const endpoint = `/protective-orders${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(endpoint);

    if (response.success && response.data) {
      return {
        data: response.data.items || [],
        count: response.data.totalCount || 0
      };
    }

    return { data: [], count: 0 };
  } catch (error) {
    throw error;
  }
}

export async function getProtectiveOrderById(
  id: string
): Promise<ProtectiveOrder | undefined> {
  try {
    const response = await apiClient.get(`/protective-orders/${id}`);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function insertProtectiveOrder(
  protectiveOrder: ProtectiveOrderDataParam
): Promise<ProtectiveOrder | undefined> {
  try {
    const response = await apiClient.post('/protective-orders', protectiveOrder);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function updateProtectiveOrder(
  protectiveOrder: ProtectiveOrderDataParam
): Promise<ProtectiveOrder | undefined> {
  try {
    const response = await apiClient.put(`/protective-orders/${protectiveOrder.id}`, protectiveOrder);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function deleteProtectiveOrder(
  protectiveOrderId: string
): Promise<ProtectiveOrder | undefined> {
  try {
    const response = await apiClient.delete(`/protective-orders/${protectiveOrderId}`);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function activateProtectiveOrder(
  protectiveOrderId: string,
  reason?: string
): Promise<ProtectiveOrder | undefined> {
  try {
    const body = reason ? { reason } : undefined;
    const response = await apiClient.patch(`/protective-orders/${protectiveOrderId}/activate`, body);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function deactivateProtectiveOrder(
  protectiveOrderId: string,
  reason: string
): Promise<ProtectiveOrder | undefined> {
  try {
    const response = await apiClient.patch(`/protective-orders/${protectiveOrderId}/deactivate`, {
      reason
    });

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}