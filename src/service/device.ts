"use client";

import { Device } from "@/data/model/device";
import { apiClient } from "@/lib/api-client";

export async function getDeviceList(
  page?: number,
  pageSize?: number,
  search?: string
): Promise<{ data: Device[]; count: number } | undefined> {
  try {
    const params = new URLSearchParams();
    if (page) params.append('page', page.toString());
    if (pageSize) params.append('pageSize', pageSize.toString());
    if (search) params.append('search', search);

    const queryString = params.toString();
    const endpoint = `/devices${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(endpoint);

    if (response.success && response.data) {
      return {
        data: response.data.devices || [],
        count: response.data.pagination?.totalCount || 0
      };
    }

    return { data: [], count: 0 };
  } catch (error) {
    throw error;
  }
}

export async function getDeviceById(
  id: string
): Promise<Device | undefined> {
  try {
    const response = await apiClient.get(`/devices/${id}`);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function insertDevice(
  device: Device
): Promise<Device | undefined> {
  try {
    const response = await apiClient.post('/devices', device);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function updateDevice(
  device: Device
): Promise<Device | undefined> {
  try {
    const response = await apiClient.put(`/devices/${device.id}`, device);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function deleteDevice(
  deviceId: string
): Promise<Device | undefined> {
  try {
    const response = await apiClient.delete(`/devices/${deviceId}`);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}