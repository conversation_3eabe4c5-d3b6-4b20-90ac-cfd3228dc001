"use client";

import { User, UserDataParam } from "@/data/model/user";
import { apiClient } from "@/lib/api-client";

export async function getUserList(
  page?: number,
  pageSize?: number,
  search?: string
): Promise<{ data: User[]; count: number } | undefined> {
  try {
    const params = new URLSearchParams();
    if (page) params.append("page", page.toString());
    if (pageSize) params.append("pageSize", pageSize.toString());
    if (search) params.append("search", search);

    const queryString = params.toString();
    const endpoint = `/users${queryString ? `?${queryString}` : ""}`;

    const response = await apiClient.get(endpoint);

    if (response.success && response.data) {
      return {
        data: response.data.users || [],
        count: response.data.pagination?.totalCount || 0,
      };
    }

    return { data: [], count: 0 };
  } catch (error) {
    throw error;
  }
}

export async function getUserById(id: string): Promise<User | undefined> {
  try {
    const response = await apiClient.get(`/users/${id}`);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function insertUser(
  user: UserDataParam
): Promise<User | undefined> {
  try {
    const response = await apiClient.post("/users", user);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function updateUser(
  user: UserDataParam
): Promise<User | undefined> {
  try {
    const response = await apiClient.put(`/users/${user.id}`, user);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function deleteUser(userId: string): Promise<User | undefined> {
  try {
    const response = await apiClient.delete(`/users/${userId}`);

    if (response.success && response.data) {
      return response.data;
    }

    return undefined;
  } catch (error) {
    throw error;
  }
}

export async function getUsersByRole(role: string): Promise<User[]> {
  try {
    const params = new URLSearchParams();
    params.append("role", role);

    const endpoint = `/users?${params.toString()}`;
    const response = await apiClient.get(endpoint);

    if (response.success && response.data) {
      return response.data.users || [];
    }

    return [];
  } catch (error) {
    console.error("Erro ao buscar usuários por papel:", error);
    return [];
  }
}

export async function getEligibleUsersForDevices(): Promise<User[]> {
  try {
    const [victims, aggressors] = await Promise.all([
      getUsersByRole("victim"),
      getUsersByRole("aggressor")
    ]);

    return [...victims, ...aggressors].sort((a, b) => {
      const nameA = a.name || a.email || "";
      const nameB = b.name || b.email || "";
      return nameA.localeCompare(nameB);
    });
  } catch (error) {
    console.error("Erro ao buscar usuários elegíveis para dispositivos:", error);
    return [];
  }
}
