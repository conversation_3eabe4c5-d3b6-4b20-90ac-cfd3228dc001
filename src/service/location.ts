"use client";

import { apiClient } from "@/lib/api-client";
import { LocationData, LocationResponse } from "@/data/model/location";

export async function getLastLocation(uuid: string): Promise<LocationResponse | undefined> {
  try {
    const response = await apiClient.get<LocationData>(`/location/last/${uuid}`);

    if (response.success && response.data) {
      return {
        success: true,
        message: response.message,
        data: response.data,
        errors: response.errors || [],
        timestamp: response.timestamp,
      };
    }

    return {
      success: false,
      message: response.message || "Localização não encontrada",
      data: null,
      errors: response.errors || [],
      timestamp: response.timestamp,
    };
  } catch (error) {
    throw error;
  }
}