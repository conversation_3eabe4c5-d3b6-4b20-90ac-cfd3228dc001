{"name": "protetor-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.835.0", "@aws-sdk/lib-storage": "^3.835.0", "@googlemaps/js-api-loader": "^1.16.10", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-table": "^8.20.5", "@types/google.maps": "^3.58.1", "axios": "^1.11.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.452.0", "next": "14.2.15", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.0", "recharts": "^2.15.0", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "wavesurfer.js": "^7.9.5", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "shadcn": "^3.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}