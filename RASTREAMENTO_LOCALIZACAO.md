# Funcionalidade de Rastreamento de Localização

## Visão Geral

Esta funcionalidade implementa o rastreamento automático das posições da vítima e do agressor no mapa a cada 5 segundos, após uma violação ser selecionada. A implementação é robusta e não afeta o funcionamento existente caso ocorram problemas na obtenção das localizações.

## Arquivos Criados/Modificados

### Novos Arquivos

1. **`src/hooks/use-location-tracking.ts`**
   - Hook customizado para gerenciar o polling de localizações
   - Faz requisições automáticas a cada 5 segundos
   - Gerencia estados de loading e erro
   - Implementa cleanup automático para evitar vazamentos de memória

2. **`src/components/google-map-with-tracking.tsx`**
   - Componente de mapa que integra rastreamento em tempo real
   - Atualiza marcadores dinamicamente
   - Mantém funcionalidade atual caso rastreamento falhe
   - Exibe indicadores visuais de status do rastreamento

3. **`src/app/api/location/last/[uuid]/route.ts`**
   - API mock para simular endpoint de localização
   - Simula movimento realista dos usuários
   - Inclui tratamento de erros e validações
   - Resposta compatível com o exemplo fornecido

### Arquivos Modificados

1. **`src/data/model/violation.ts`**
   - Adicionados campos opcionais `victimId` e `aggressorId`
   - Campos são opcionais para manter compatibilidade

2. **`src/data/mock/violations-mock.ts`**
   - Adicionados IDs para todas as violações mock
   - Dados consistentes para teste da funcionalidade

3. **`src/components/violations-dashboard.tsx`**
   - Atualizado para usar novo componente com rastreamento
   - Passa IDs e status de ativação para o mapa

## Como Funciona

### 1. Seleção de Violação
- Quando uma violação é selecionada, o sistema verifica se há `victimId` e `aggressorId`
- Se a violação está ativa (`isActive: true`) e tem os IDs, o rastreamento é iniciado

### 2. Polling Automático
- O hook `useLocationTracking` inicia polling a cada 5 segundos
- Faz requisições paralelas para ambos os endpoints (`/api/location/last/[uuid]`)
- Atualiza apenas se receber dados válidos

### 3. Atualização do Mapa
- Componente `GoogleMapWithTracking` escuta mudanças nas localizações
- Atualiza posições dos marcadores sem recriar o mapa
- Mantém performance otimizada

### 4. Fallback e Robustez
- Se o rastreamento falhar, usa localizações iniciais
- Não interrompe funcionamento atual
- Logs de erro apenas no console (não afeta UX)

## Recursos Implementados

### Indicadores Visuais
- **Ícone de WiFi**: Indica status do rastreamento
- **Timestamp**: Mostra última atualização bem-sucedida
- **Animação de loading**: Durante busca de dados

### Otimizações de Performance
- Polling ativo apenas para violações selecionadas e ativas
- Cleanup automático de intervalos
- Requisições paralelas para otimizar tempo
- Cache de posições anteriores

### Tratamento de Erros
- Falhas silenciosas (não afetam UX)
- Retry automático no próximo ciclo
- Fallback para localizações iniciais
- Logs detalhados para debug

## Configuração

### Variáveis de Ambiente
```env
# Não há variáveis adicionais necessárias
# API mock funciona automaticamente
```

### Endpoint Real
Para usar endpoint real, substitua a URL no hook:
```typescript
const response = await fetch(`/api/location/last/${uuid}`);
// Por:
const response = await fetch(`https://sua-api.com/api/location/last/${uuid}`);
```

## Exemplo de Resposta da API

```json
{
  "success": true,
  "message": "Última localização recuperada com sucesso",
  "data": {
    "id": "c13faaf8-826d-4f31-b658-9893a09212b7",
    "latitude": -23.42554800,
    "longitude": -51.94157500,
    "timestamp": "2025-09-02T07:17:48.672729Z",
    "createdAt": "2025-09-02T07:17:48.672729Z"
  },
  "errors": [],
  "timestamp": "2025-09-03T15:58:09.686058Z"
}
```

## Compatibilidade

### Backward Compatibility
- ✅ Funciona com dados existentes sem IDs
- ✅ Não quebra violações sem rastreamento
- ✅ Mantém comportamento atual em casos de erro

### Forward Compatibility
- ✅ Pronto para integrar com API real
- ✅ Configurável interval de polling
- ✅ Extensível para outros tipos de rastreamento

## Testando a Funcionalidade

1. **Executar aplicação**: `npm run dev`
2. **Abrir página inicial**: Navegar para `/home`
3. **Selecionar violação ativa**: Clique em violação com `isActive: true`
4. **Verificar indicadores**: 
   - Ícone WiFi no canto superior direito
   - Timestamp da última atualização
   - Movimento gradual dos marcadores

## Próximos Passos

1. **Integrar com API real**: Substituir mock por endpoint real
2. **Adicionar alertas**: Notificações quando distância mínima for violada
3. **Histórico de movimentos**: Salvar rastro de localizações
4. **Configuração de intervalo**: Permitir ajustar frequência via settings