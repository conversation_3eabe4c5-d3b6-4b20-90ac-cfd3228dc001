# Guia de Centralização Automática do Mapa

## Funcionalidade Implementada

Quando uma violação é selecionada na lista, o mapa é automaticamente centralizado para mostrar os marcadores da vítima e do agressor na tela, com zoom adequado baseado na distância entre eles.

## Como Funciona

### 1. Detecção de Nova Violação
O sistema detecta mudanças nas coordenadas iniciais (`initialVictimLocation` e `initialAggressorLocation`) e identifica quando uma nova violação é selecionada.

### 2. Centralização Inteligente
- **Bounds Calculation**: Cria um retângulo que engloba ambos os marcadores
- **Margin Padding**: Adiciona margens de 60px em todos os lados para melhor visualização  
- **Smooth Animation**: Usa `fitBounds()` do Google Maps para transição suave
- **Zoom Control**: Limita zoom máximo em 18 para evitar proximidade excessiva

### 3. Indicadores Visuais
- **Loading Indicator**: Mostra "Centralizando mapa..." durante a transição
- **Duration**: Indicador aparece por 1.2 segundos durante a animação

## Cenários de Teste

### Teste 1: Violações em São Paulo
1. Selecione `violation-001` (São Paulo)
2. Observe centralização nos marcadores próximos (~150m de distância)
3. Mude para `violation-002` (Rio de Janeiro)  
4. Verifique transição suave entre cidades

### Teste 2: Diferentes Distâncias
1. `violation-003` (Porto Alegre) - distância moderada
2. `violation-006` (Brasília) - marcadores muito próximos
3. Observe ajuste automático do zoom baseado na distância

### Teste 3: Transições Rápidas
1. Clique rapidamente entre diferentes violações
2. Verifique que cada transição completa antes da próxima
3. Sem sobreposição de animações

## Comportamento Técnico

### Estados de Centralização
```typescript
interface CenteringBehavior {
  trigger: "violation selection change";
  detection: "initial coordinates comparison";
  method: "Google Maps fitBounds()";
  animation: "smooth transition";
  duration: "1200ms";
  padding: "60px all sides";
  maxZoom: 18;
}
```

### Logs de Debug
No console do desenvolvedor, você verá:
```
✅ "Nova violação selecionada, centralizando mapa"
```

## Configurações Personalizáveis

### Ajustar Margens do Mapa
```typescript
// Em google-map-with-tracking.tsx
mapInstanceRef.current.fitBounds(bounds, {
  top: 60,     // margem superior
  right: 60,   // margem direita  
  bottom: 60,  // margem inferior
  left: 60,    // margem esquerda
});
```

### Modificar Tempo de Transição
```typescript
setTimeout(() => {
  // ações pós-centralização
  setIsRecenteringMap(false);
}, 1200); // alterar duração aqui
```

### Ajustar Zoom Máximo
```typescript
if (currentZoom && currentZoom > 18) {
  mapInstanceRef.current.setZoom(18); // alterar limite aqui
}
```

## Otimizações Implementadas

### Performance
- **Single Effect**: Um useEffect detecta mudanças nas coordenadas iniciais
- **Debouncing**: Não executa se o mapa ainda está carregando
- **Reference Comparison**: Compara coordenadas por valor, não referência

### UX
- **Visual Feedback**: Indicador de loading durante transição
- **Smooth Animation**: Usa API nativa do Google Maps para suavidade
- **Error Handling**: Funciona mesmo se APIs falham

### Robustez
- **Null Checks**: Verifica se mapa e Google Maps API estão disponíveis
- **State Management**: Controla estado de recentralização separadamente
- **Cleanup**: Remove timers quando componente é desmontado

## Integração com Rastreamento

A centralização funciona independentemente do rastreamento em tempo real:

1. **Primeira Centralização**: Quando violação é selecionada (localizações iniciais)
2. **Atualizações Contínuas**: Marcadores se movem conforme rastreamento, mas mapa mantém enquadramento
3. **Reconcentração**: Apenas quando nova violação é selecionada

## Troubleshooting

### Problema: Mapa não centraliza
**Possíveis causas:**
- Google Maps API não carregou
- Componente ainda em estado de loading
- Coordenadas inválidas

**Solução:**
```bash
# Verificar console por erros
# Logs esperados:
✅ "Nova violação selecionada, centralizando mapa"
✅ Sem erros do Google Maps API
```

### Problema: Centralização muito rápida/lenta
**Ajustar timing:**
```typescript
// Reduzir duração do indicador
setTimeout(() => setIsRecenteringMap(false), 800); // mais rápido
```

### Problema: Zoom inadequado
**Modificar lógica de zoom:**
```typescript
// Para zoom mais distante
if (currentZoom && currentZoom > 15) { // era 18
  mapInstanceRef.current.setZoom(15);
}
```

A funcionalidade garante que toda vez que o usuário selecionar uma violação, os marcadores estarão perfeitamente visíveis no mapa com enquadramento ideal!