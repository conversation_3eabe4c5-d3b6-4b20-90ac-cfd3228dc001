# Componente de Mapa Google Maps

## Visão Geral

Foi criado um sistema de visualização de mapas integrado ao dashboard de violações, que permite visualizar as posições da vítima e do agressor em um mapa interativo do Google Maps.

## Funcionalidades

### 🗺️ Mapa Interativo
- **Visualização das localizações**: Vítima (marcador azul) e agressor (marcador vermelho)
- **Cálculo automático de distância**: <PERSON><PERSON> conectando as duas posições
- **Zoom otimizado**: Ajusta automaticamente para mostrar ambas as posições
- **Info windows**: Detalhes ao clicar nos marcadores
- **Legenda**: Identificação visual dos marcadores

### 🎯 Seleção de Violações
- **Cards interativos**: Clique em qualquer violação para visualizar no mapa
- **Indicador visual**: Violação selecionada fica destacada
- **Estado persistente**: Mapa atualiza conforme seleção
- **Validação de dados**: <PERSON>ó mostra botão se há coordenadas válidas

### 📊 Dashboard Integrado
- **Layout responsivo**: Lista de violações + mapa lado a lado
- **Detalhes contextuais**: Informações da violação selecionada
- **Status da violação**: Indica se está conforme ou violando distância mínima
- **Design consistente**: Segue padrões do projeto existente

## Arquivos Criados/Modificados

### Novos Componentes
- `src/components/google-map.tsx` - Componente principal do mapa
- `src/components/violations-dashboard.tsx` - Dashboard integrado
- `.env.example` - Configuração da API key

### Componentes Modificados
- `src/components/violations/violation-card.tsx` - Adicionado seleção e botão do mapa
- `src/components/violations/violations-content.tsx` - Props para seleção
- `src/components/violations-list.tsx` - Props para seleção
- `src/app/(user-app)/home/<USER>

### Dependências Adicionadas
- `@googlemaps/js-api-loader` - Carregamento da API do Google Maps
- `@types/google.maps` - Tipos TypeScript

## Configuração

### 1. Google Maps API Key

1. Acesse o [Google Cloud Console](https://console.cloud.google.com/)
2. Crie/selecione um projeto
3. Ative a API "Maps JavaScript API"
4. Crie uma API Key
5. Configure as restrições (opcional mas recomendado)

### 2. Variável de Ambiente

Copie o arquivo `.env.example` para `.env.local`:

```bash
cp .env.example .env.local
```

Edite o arquivo `.env.local` e adicione sua API key:

```bash
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=sua_api_key_aqui
```

## Uso

### Dashboard Principal
O dashboard é carregado automaticamente na página `/home` e inclui:
- Lista de violações à esquerda
- Mapa à direita
- Seleção interativa entre os dois

### Integração com Outros Componentes

```tsx
import { ViolationsDashboard } from "@/components/violations-dashboard";

export default function MyPage() {
  return <ViolationsDashboard />;
}
```

### Uso Direto do Mapa

```tsx
import { GoogleMap } from "@/components/google-map";

<GoogleMap
  victimLocation={{ latitude: -23.5505, longitude: -46.6333 }}
  aggressorLocation={{ latitude: -23.5515, longitude: -46.6343 }}
  className="h-96 w-full"
  onMapLoad={(map) => console.log('Mapa carregado:', map)}
/>
```

## Características Técnicas

### Clean Code
- **Separação de responsabilidades**: Cada componente tem uma função específica
- **Nomes descritivos**: Funções e variáveis com nomes claros
- **Funções pequenas**: Cada função tem uma responsabilidade única
- **Tratamento de erros**: Estados de loading, erro e validação
- **Types seguros**: TypeScript com tipagem adequada

### Performance
- **Lazy loading**: Mapa só carrega quando necessário
- **Memoização**: Cálculos otimizados
- **Estados controlados**: Re-renders minimizados
- **Clean up**: Recursos liberados adequadamente

### UX/UI
- **Feedback visual**: Estados de loading e erro
- **Responsivo**: Funciona em desktop e mobile
- **Acessibilidade**: Labels e controles adequados
- **Consistência**: Segue design system do projeto

## Limitações Conhecidas

1. **Requer internet**: Google Maps precisa de conexão
2. **Cota de API**: Sujeito aos limites do Google Cloud
3. **Coordenadas válidas**: Só funciona com lat/lng não-zero
4. **Browser support**: Navegadores modernos apenas

## Próximos Passos Sugeridos

### Melhorias Futuras
- [ ] Mapa em tela cheia
- [ ] Histórico de movimentação (polyline temporal)
- [ ] Círculo de distância mínima
- [ ] Exportar coordenadas
- [ ] Integração com Mapbox (alternativa)
- [ ] Modo offline com cache
- [ ] Geofencing em tempo real

### Funcionalidades Avançadas
- [ ] Heat map de violações
- [ ] Clustering de marcadores
- [ ] Rotas de fuga/segurança
- [ ] Alertas por proximidade
- [ ] Integração com GPS real-time

## Troubleshooting

### Mapa não carrega
1. Verifique se a API key está configurada
2. Confirme se a API "Maps JavaScript API" está ativa
3. Verifique o console do navegador para erros
4. Teste a conectividade com a internet

### Performance lenta
1. Verifique a latência da rede
2. Considere reduzir a resolução do mapa
3. Implemente caching se necessário
4. Monitore o uso da cota da API

### Dados não aparecem
1. Confirme se `victimStartLocation` e `aggressorStartLocation` estão definidos
2. Verifique se latitude/longitude não são zero
3. Teste com dados mock primeiro
4. Valide o formato dos dados de violação