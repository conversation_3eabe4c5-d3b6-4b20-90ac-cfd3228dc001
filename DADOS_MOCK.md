# Sistema de Dados Mock para Violações

## Visão Geral

Foi implementado um sistema completo de dados simulados (mock) para permitir o teste das funcionalidades de mapa e violações mesmo quando a API real não está disponível ou não retorna dados.

## Funcionalidades do Mock

### 🎲 Dados Simulados Realistas
- **7 violações diferentes** com dados variados
- **Localizações reais** em grandes cidades brasileiras:
  - São Paulo (Praça da Sé)
  - Rio de Janeiro (Copacabana)  
  - Porto Alegre (Centro)
  - Belo Horizonte (Savassi)
  - Recife (Centro)
  - Brasília (Plano Piloto)
  - Fortaleza (Praia de Iracema)

### 📊 Variedade de Cenários
- **Violações ativas e inativas** 
- **Distâncias diferentes** (150m a 1000m)
- **Tempos variados** (15 minutos a 8 horas atrás)
- **Casos de violação** e **casos conformes**
- **Nomes realistas** de vítimas e agressores
- **Números de processos** judiciais simulados

### 🔄 Comportamento Dinâmico
- **Latência simulada** (500-1500ms)
- **5% de chance de erro** para testar tratamento de falhas
- **10% de chance** de mudança de status a cada carregamento
- **Atualizações automáticas** a cada 10 segundos

## Configuração

### Modo Mock
No arquivo `.env.local`:
```bash
# Usar sempre dados mock (ignora API real)
NEXT_PUBLIC_USE_MOCK_DATA=true
```

### Modo Híbrido (Recomendado)
```bash
# Tenta API real primeiro, usa mock como fallback
NEXT_PUBLIC_USE_MOCK_DATA=false
```

## Estrutura dos Dados Mock

### Localização das Violações
```
São Paulo:     -23.5505, -46.6333 (VIOLAÇÃO - 150m de distância)
Rio de Janeiro: -22.9068, -43.1729 (CONFORME - 500m+ de distância) 
Porto Alegre:   -30.0346, -51.2177 (CONFORME)
Belo Horizonte: -19.9191, -43.9386 (VIOLAÇÃO - 400m de distância)
Recife:         -8.0476,  -34.8770 (CONFORME)
Brasília:       -15.7942, -47.8822 (VIOLAÇÃO - 140m de distância)
Fortaleza:      -3.7319,  -38.5267 (CONFORME)
```

### Exemplos de Casos

#### Violação Crítica
```
Vítima: Maria Silva Santos
Agressor: João Carlos Oliveira  
Localização: São Paulo - Praça da Sé
Distância: ~150m (VIOLAÇÃO)
Status: Ativa
```

#### Caso Conforme
```
Vítima: Ana Paula Costa
Agressor: Carlos Eduardo Lima
Localização: Rio de Janeiro - Copacabana  
Distância: ~500m (CONFORME)
Status: Ativa
```

## Funcionalidades para Teste

### 🗺️ Teste do Mapa
- **Zoom automático** baseado na distância entre vítima e agressor
- **Marcadores coloridos** (azul = vítima, vermelho = agressor)
- **Cálculo de distância** em tempo real
- **Status visual** (VIOLAÇÃO/CONFORME)

### 📱 Teste da Interface
- **Cards interativos** que mostram diferentes estados
- **Seleção visual** da violação ativa
- **Estados de loading** e refresh
- **Tratamento de erros** simulado

### ⏱️ Teste de Tempo Real
- **Auto-refresh** a cada 10 segundos
- **Mudanças dinâmicas** de status
- **Simulação de novas violações**
- **Horários realistas** baseados no momento atual

## Arquivos Relacionados

### Criados/Modificados
- `src/data/mock/violations-mock.ts` - Dados e lógica mock
- `src/hooks/use-violations.ts` - Hook modificado com fallback
- `.env.local` - Configuração de modo mock
- `.env.example` - Documentação da configuração

### Integração
O sistema de mock se integra perfeitamente com:
- `ViolationsList` - Lista de violações
- `GoogleMap` - Componente de mapa  
- `ViolationsDashboard` - Dashboard principal
- Todo o sistema de tipos TypeScript

## Como Usar

### 1. Desenvolvimento Local
```bash
# 1. Configure o arquivo .env.local
NEXT_PUBLIC_USE_MOCK_DATA=true
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=sua_chave_aqui

# 2. Inicie o servidor
npm run dev

# 3. Acesse http://localhost:3000/home
```

### 2. Teste de Funcionalidades
1. **Lista de violações** carrega automaticamente
2. **Clique em qualquer violação** para ver no mapa
3. **Observe diferentes estados** (ativa/inativa, violação/conforme)
4. **Teste o refresh** clicando no botão de atualizar
5. **Aguarde auto-refresh** para ver mudanças dinâmicas

### 3. Teste de Erros
- O mock simula 5% de chance de falha
- Console mostra logs quando usa fallback
- Interface trata erros graciosamente

## Logs do Console

### Modo Mock Ativo
```
✓ Usando dados mock (NEXT_PUBLIC_USE_MOCK_DATA=true)
```

### Modo Fallback
```
⚠ API real falhou, usando dados mock como fallback: [erro]
```

### Dados Carregados
```
📊 7 violações carregadas (5 ativas, 2 inativas)
```

## Benefícios para Desenvolvimento

### ✅ Teste Independente
- Não depende de API externa
- Funciona offline
- Dados sempre disponíveis

### ✅ Cenários Completos
- Casos de sucesso e erro
- Diferentes distâncias e status
- Atualizações em tempo real

### ✅ Debug Facilitado
- Dados previsíveis e controláveis
- Logs claros no console
- Estados visuais óbvios

### ✅ Demo Ready
- Dados realistas para apresentações
- Comportamento confiável
- Performance consistente

## Transição para Produção

Quando a API real estiver pronta:

1. **Configure .env.local**:
   ```bash
   NEXT_PUBLIC_USE_MOCK_DATA=false
   ```

2. **O sistema automaticamente**:
   - Tenta usar a API real primeiro
   - Usa mock como fallback se a API falhar
   - Logs indicam qual fonte está sendo usada

3. **Para produção**:
   - Remova a variável `NEXT_PUBLIC_USE_MOCK_DATA` 
   - O sistema usará apenas a API real
   - Mock permanece como fallback de segurança

## Personalização

### Adicionar Novas Violações
Edite `src/data/mock/violations-mock.ts`:

```typescript
const novaViolacao: ViolationEventResponse = {
  id: "violation-008",
  // ... outros campos
  victimStartLocation: {
    latitude: -12.9714, // Sua cidade
    longitude: -38.5014,
  },
  aggressorStartLocation: {
    latitude: -12.9720, // Posição do agressor  
    longitude: -38.5020,
  },
};
```

### Ajustar Comportamento
- **Latência**: Modifique `delay()` em `violations-mock.ts`
- **Taxa de erro**: Ajuste `Math.random() < 0.05` (5%)
- **Mudanças dinâmicas**: Ajuste `Math.random() < 0.1` (10%)
- **Intervalo de refresh**: Modifique `REFRESH_INTERVAL` em `use-violations.ts`